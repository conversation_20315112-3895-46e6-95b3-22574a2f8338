#############################################################################
# Makefile for building: OurChat
# Generated by qmake (3.1) (Qt 6.5.3)
# Project:  ..\..\..\..\OurChat2\Client\OurChat.pro
# Template: app
# Command: G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\bin\qmake.exe -o Makefile ..\..\..\..\OurChat2\Client\OurChat.pro -spec win32-msvc "CONFIG+=debug" "CONFIG+=qml_debug"
#############################################################################

MAKEFILE      = Makefile

EQ            = =

first: debug
install: debug-install
uninstall: debug-uninstall
QMAKE         = G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\bin\qmake.exe
DEL_FILE      = del
CHK_DIR_EXISTS= if not exist
MKDIR         = mkdir
COPY          = copy /y
COPY_FILE     = copy /y
COPY_DIR      = xcopy /s /q /y /i
INSTALL_FILE  = copy /y
INSTALL_PROGRAM = copy /y
INSTALL_DIR   = xcopy /s /q /y /i
QINSTALL      = G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\bin\qmake.exe -install qinstall
QINSTALL_PROGRAM = G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\bin\qmake.exe -install qinstall -exe
DEL_FILE      = del
SYMLINK       = $(QMAKE) -install ln -f -s
DEL_DIR       = rmdir
MOVE          = move
IDC           = idc
IDL           = midl
ZIP           = zip -r -9
DEF_FILE      = 
RES_FILE      = 
SED           = $(QMAKE) -install sed
MOVE          = move
SUBTARGETS    =  \
		debug \
		release


debug: $(MAKEFILE) FORCE
	@set MAKEFLAGS=$(MAKEFLAGS)
	$(MAKE) -f $(MAKEFILE).Debug
debug-make_first: FORCE
	@set MAKEFLAGS=$(MAKEFLAGS)
	$(MAKE) -f $(MAKEFILE).Debug 
debug-all: FORCE
	@set MAKEFLAGS=$(MAKEFLAGS)
	$(MAKE) -f $(MAKEFILE).Debug all
debug-clean: FORCE
	@set MAKEFLAGS=$(MAKEFLAGS)
	$(MAKE) -f $(MAKEFILE).Debug clean
debug-distclean: FORCE
	@set MAKEFLAGS=$(MAKEFLAGS)
	$(MAKE) -f $(MAKEFILE).Debug distclean
debug-install: FORCE
	@set MAKEFLAGS=$(MAKEFLAGS)
	$(MAKE) -f $(MAKEFILE).Debug install
debug-uninstall: FORCE
	@set MAKEFLAGS=$(MAKEFLAGS)
	$(MAKE) -f $(MAKEFILE).Debug uninstall
release: $(MAKEFILE) FORCE
	@set MAKEFLAGS=$(MAKEFLAGS)
	$(MAKE) -f $(MAKEFILE).Release
release-make_first: FORCE
	@set MAKEFLAGS=$(MAKEFLAGS)
	$(MAKE) -f $(MAKEFILE).Release 
release-all: FORCE
	@set MAKEFLAGS=$(MAKEFLAGS)
	$(MAKE) -f $(MAKEFILE).Release all
release-clean: FORCE
	@set MAKEFLAGS=$(MAKEFLAGS)
	$(MAKE) -f $(MAKEFILE).Release clean
release-distclean: FORCE
	@set MAKEFLAGS=$(MAKEFLAGS)
	$(MAKE) -f $(MAKEFILE).Release distclean
release-install: FORCE
	@set MAKEFLAGS=$(MAKEFLAGS)
	$(MAKE) -f $(MAKEFILE).Release install
release-uninstall: FORCE
	@set MAKEFLAGS=$(MAKEFLAGS)
	$(MAKE) -f $(MAKEFILE).Release uninstall

Makefile: ..\..\..\..\OurChat2\Client\OurChat.pro G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\win32-msvc\qmake.conf G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\features\spec_pre.prf \
		G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\common\windows-desktop.conf \
		G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\features\win32\windows_vulkan_sdk.prf \
		G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\common\windows-vulkan.conf \
		G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\common\msvc-desktop.conf \
		G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\qconfig.pri \
		G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_ext_freetype.pri \
		G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_ext_libjpeg.pri \
		G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_ext_libpng.pri \
		G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_concurrent.pri \
		G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_concurrent_private.pri \
		G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_core.pri \
		G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_core_private.pri \
		G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_dbus.pri \
		G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_dbus_private.pri \
		G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_designer.pri \
		G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_designer_private.pri \
		G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_designercomponents_private.pri \
		G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_devicediscovery_support_private.pri \
		G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_entrypoint_private.pri \
		G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_example_icons_private.pri \
		G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_fb_support_private.pri \
		G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_freetype_private.pri \
		G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_gui.pri \
		G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_gui_private.pri \
		G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_harfbuzz_private.pri \
		G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_help.pri \
		G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_help_private.pri \
		G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_httpserver.pri \
		G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_httpserver_private.pri \
		G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_jpeg_private.pri \
		G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_labsanimation.pri \
		G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_labsanimation_private.pri \
		G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_labsfolderlistmodel.pri \
		G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_labsfolderlistmodel_private.pri \
		G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_labsqmlmodels.pri \
		G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_labsqmlmodels_private.pri \
		G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_labssettings.pri \
		G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_labssettings_private.pri \
		G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_labssharedimage.pri \
		G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_labssharedimage_private.pri \
		G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_labswavefrontmesh.pri \
		G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_labswavefrontmesh_private.pri \
		G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_linguist.pri \
		G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_linguist_private.pri \
		G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_network.pri \
		G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_network_private.pri \
		G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_opengl.pri \
		G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_opengl_private.pri \
		G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_openglwidgets.pri \
		G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_openglwidgets_private.pri \
		G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_packetprotocol_private.pri \
		G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_png_private.pri \
		G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_positioning.pri \
		G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_positioning_private.pri \
		G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_positioningquick.pri \
		G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_positioningquick_private.pri \
		G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_printsupport.pri \
		G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_printsupport_private.pri \
		G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_qml.pri \
		G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_qml_private.pri \
		G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_qmlcompiler_private.pri \
		G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_qmlcore.pri \
		G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_qmlcore_private.pri \
		G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_qmldebug_private.pri \
		G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_qmldom_private.pri \
		G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_qmlintegration.pri \
		G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_qmlintegration_private.pri \
		G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_qmllocalstorage.pri \
		G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_qmllocalstorage_private.pri \
		G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_qmlmodels.pri \
		G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_qmlmodels_private.pri \
		G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_qmltest.pri \
		G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_qmltest_private.pri \
		G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_qmltyperegistrar_private.pri \
		G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_qmlworkerscript.pri \
		G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_qmlworkerscript_private.pri \
		G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_qmlxmllistmodel.pri \
		G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_qmlxmllistmodel_private.pri \
		G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quick.pri \
		G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quick_private.pri \
		G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quickcontrols2.pri \
		G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quickcontrols2_private.pri \
		G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quickcontrols2impl.pri \
		G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quickcontrols2impl_private.pri \
		G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quickcontrolstestutilsprivate_private.pri \
		G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quickdialogs2.pri \
		G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quickdialogs2_private.pri \
		G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quickdialogs2quickimpl.pri \
		G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quickdialogs2quickimpl_private.pri \
		G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quickdialogs2utils.pri \
		G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quickdialogs2utils_private.pri \
		G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quickeffects_private.pri \
		G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quicklayouts.pri \
		G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quicklayouts_private.pri \
		G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quickparticles_private.pri \
		G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quickshapes_private.pri \
		G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quicktemplates2.pri \
		G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quicktemplates2_private.pri \
		G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quicktestutilsprivate_private.pri \
		G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quickwidgets.pri \
		G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quickwidgets_private.pri \
		G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_sql.pri \
		G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_sql_private.pri \
		G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_svg.pri \
		G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_svg_private.pri \
		G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_svgwidgets.pri \
		G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_svgwidgets_private.pri \
		G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_testlib.pri \
		G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_testlib_private.pri \
		G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_tools_private.pri \
		G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_uiplugin.pri \
		G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_uitools.pri \
		G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_uitools_private.pri \
		G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_webchannel.pri \
		G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_webchannel_private.pri \
		G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_webenginecore.pri \
		G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_webenginecore_private.pri \
		G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_webenginequick.pri \
		G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_webenginequick_private.pri \
		G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_webenginequickdelegatesqml.pri \
		G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_webenginequickdelegatesqml_private.pri \
		G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_webenginewidgets.pri \
		G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_webenginewidgets_private.pri \
		G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_websockets.pri \
		G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_websockets_private.pri \
		G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_widgets.pri \
		G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_widgets_private.pri \
		G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_xml.pri \
		G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_xml_private.pri \
		G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_zlib_private.pri \
		G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\features\qt_functions.prf \
		G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\features\qt_config.prf \
		G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\win32-msvc\qmake.conf \
		G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\features\spec_post.prf \
		.qmake.stash \
		G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\features\exclusive_builds.prf \
		G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\common\msvc-version.conf \
		G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\features\toolchain.prf \
		G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\features\default_pre.prf \
		G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\features\win32\default_pre.prf \
		G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\features\resolve_config.prf \
		G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\features\exclusive_builds_post.prf \
		G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\features\default_post.prf \
		G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\features\qml_debug.prf \
		G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\features\entrypoint.prf \
		G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\features\precompile_header.prf \
		G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\features\warn_on.prf \
		G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\features\qt.prf \
		G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\features\resources_functions.prf \
		G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\features\resources.prf \
		G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\features\moc.prf \
		G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\features\win32\opengl.prf \
		G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\features\uic.prf \
		G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\features\qmake_use.prf \
		G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\features\file_copies.prf \
		G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\features\win32\windows.prf \
		G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\features\testcase_targets.prf \
		G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\features\exceptions.prf \
		G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\features\yacc.prf \
		G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\features\lex.prf \
		..\..\..\..\OurChat2\Client\OurChat.pro \
		G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\lib\Qt6OpenGLWidgets.prl \
		G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\lib\Qt6OpenGL.prl \
		G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\lib\Qt6Widgets.prl \
		G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\lib\Qt6Gui.prl \
		G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\lib\Qt6Network.prl \
		G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\lib\Qt6Core.prl \
		G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\lib\Qt6EntryPoint.prl \
		G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\features\build_pass.prf \
		..\..\..\..\OurChat2\Client\src.qrc \
		G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\lib\Qt6OpenGLWidgetsd.prl \
		G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\lib\Qt6OpenGLd.prl \
		G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\lib\Qt6Widgetsd.prl \
		G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\lib\Qt6Guid.prl \
		G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\lib\Qt6Networkd.prl \
		G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\lib\Qt6Cored.prl \
		G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\lib\Qt6EntryPointd.prl
	$(QMAKE) -o Makefile ..\..\..\..\OurChat2\Client\OurChat.pro -spec win32-msvc "CONFIG+=debug" "CONFIG+=qml_debug"
G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\features\spec_pre.prf:
G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\common\windows-desktop.conf:
G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\features\win32\windows_vulkan_sdk.prf:
G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\common\windows-vulkan.conf:
G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\common\msvc-desktop.conf:
G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\qconfig.pri:
G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_ext_freetype.pri:
G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_ext_libjpeg.pri:
G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_ext_libpng.pri:
G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_concurrent.pri:
G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_concurrent_private.pri:
G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_core.pri:
G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_core_private.pri:
G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_dbus.pri:
G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_dbus_private.pri:
G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_designer.pri:
G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_designer_private.pri:
G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_designercomponents_private.pri:
G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_devicediscovery_support_private.pri:
G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_entrypoint_private.pri:
G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_example_icons_private.pri:
G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_fb_support_private.pri:
G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_freetype_private.pri:
G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_gui.pri:
G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_gui_private.pri:
G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_harfbuzz_private.pri:
G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_help.pri:
G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_help_private.pri:
G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_httpserver.pri:
G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_httpserver_private.pri:
G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_jpeg_private.pri:
G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_labsanimation.pri:
G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_labsanimation_private.pri:
G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_labsfolderlistmodel.pri:
G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_labsfolderlistmodel_private.pri:
G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_labsqmlmodels.pri:
G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_labsqmlmodels_private.pri:
G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_labssettings.pri:
G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_labssettings_private.pri:
G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_labssharedimage.pri:
G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_labssharedimage_private.pri:
G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_labswavefrontmesh.pri:
G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_labswavefrontmesh_private.pri:
G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_linguist.pri:
G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_linguist_private.pri:
G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_network.pri:
G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_network_private.pri:
G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_opengl.pri:
G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_opengl_private.pri:
G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_openglwidgets.pri:
G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_openglwidgets_private.pri:
G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_packetprotocol_private.pri:
G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_png_private.pri:
G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_positioning.pri:
G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_positioning_private.pri:
G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_positioningquick.pri:
G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_positioningquick_private.pri:
G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_printsupport.pri:
G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_printsupport_private.pri:
G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_qml.pri:
G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_qml_private.pri:
G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_qmlcompiler_private.pri:
G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_qmlcore.pri:
G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_qmlcore_private.pri:
G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_qmldebug_private.pri:
G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_qmldom_private.pri:
G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_qmlintegration.pri:
G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_qmlintegration_private.pri:
G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_qmllocalstorage.pri:
G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_qmllocalstorage_private.pri:
G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_qmlmodels.pri:
G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_qmlmodels_private.pri:
G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_qmltest.pri:
G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_qmltest_private.pri:
G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_qmltyperegistrar_private.pri:
G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_qmlworkerscript.pri:
G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_qmlworkerscript_private.pri:
G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_qmlxmllistmodel.pri:
G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_qmlxmllistmodel_private.pri:
G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quick.pri:
G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quick_private.pri:
G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quickcontrols2.pri:
G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quickcontrols2_private.pri:
G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quickcontrols2impl.pri:
G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quickcontrols2impl_private.pri:
G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quickcontrolstestutilsprivate_private.pri:
G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quickdialogs2.pri:
G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quickdialogs2_private.pri:
G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quickdialogs2quickimpl.pri:
G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quickdialogs2quickimpl_private.pri:
G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quickdialogs2utils.pri:
G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quickdialogs2utils_private.pri:
G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quickeffects_private.pri:
G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quicklayouts.pri:
G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quicklayouts_private.pri:
G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quickparticles_private.pri:
G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quickshapes_private.pri:
G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quicktemplates2.pri:
G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quicktemplates2_private.pri:
G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quicktestutilsprivate_private.pri:
G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quickwidgets.pri:
G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_quickwidgets_private.pri:
G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_sql.pri:
G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_sql_private.pri:
G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_svg.pri:
G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_svg_private.pri:
G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_svgwidgets.pri:
G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_svgwidgets_private.pri:
G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_testlib.pri:
G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_testlib_private.pri:
G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_tools_private.pri:
G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_uiplugin.pri:
G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_uitools.pri:
G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_uitools_private.pri:
G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_webchannel.pri:
G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_webchannel_private.pri:
G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_webenginecore.pri:
G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_webenginecore_private.pri:
G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_webenginequick.pri:
G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_webenginequick_private.pri:
G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_webenginequickdelegatesqml.pri:
G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_webenginequickdelegatesqml_private.pri:
G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_webenginewidgets.pri:
G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_webenginewidgets_private.pri:
G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_websockets.pri:
G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_websockets_private.pri:
G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_widgets.pri:
G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_widgets_private.pri:
G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_xml.pri:
G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_xml_private.pri:
G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\modules\qt_lib_zlib_private.pri:
G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\features\qt_functions.prf:
G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\features\qt_config.prf:
G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\win32-msvc\qmake.conf:
G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\features\spec_post.prf:
.qmake.stash:
G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\features\exclusive_builds.prf:
G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\common\msvc-version.conf:
G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\features\toolchain.prf:
G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\features\default_pre.prf:
G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\features\win32\default_pre.prf:
G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\features\resolve_config.prf:
G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\features\exclusive_builds_post.prf:
G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\features\default_post.prf:
G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\features\qml_debug.prf:
G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\features\entrypoint.prf:
G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\features\precompile_header.prf:
G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\features\warn_on.prf:
G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\features\qt.prf:
G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\features\resources_functions.prf:
G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\features\resources.prf:
G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\features\moc.prf:
G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\features\win32\opengl.prf:
G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\features\uic.prf:
G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\features\qmake_use.prf:
G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\features\file_copies.prf:
G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\features\win32\windows.prf:
G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\features\testcase_targets.prf:
G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\features\exceptions.prf:
G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\features\yacc.prf:
G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\features\lex.prf:
..\..\..\..\OurChat2\Client\OurChat.pro:
G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\lib\Qt6OpenGLWidgets.prl:
G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\lib\Qt6OpenGL.prl:
G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\lib\Qt6Widgets.prl:
G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\lib\Qt6Gui.prl:
G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\lib\Qt6Network.prl:
G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\lib\Qt6Core.prl:
G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\lib\Qt6EntryPoint.prl:
G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\mkspecs\features\build_pass.prf:
..\..\..\..\OurChat2\Client\src.qrc:
G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\lib\Qt6OpenGLWidgetsd.prl:
G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\lib\Qt6OpenGLd.prl:
G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\lib\Qt6Widgetsd.prl:
G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\lib\Qt6Guid.prl:
G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\lib\Qt6Networkd.prl:
G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\lib\Qt6Cored.prl:
G:\software\Qt\Qt6.5.3\6.5.3\msvc2019_64\lib\Qt6EntryPointd.prl:
qmake: FORCE
	@$(QMAKE) -o Makefile ..\..\..\..\OurChat2\Client\OurChat.pro -spec win32-msvc "CONFIG+=debug" "CONFIG+=qml_debug"

qmake_all: FORCE

make_first: debug-make_first release-make_first  FORCE
all: debug-all release-all  FORCE
clean: debug-clean release-clean  FORCE
	-$(DEL_FILE) OurChat.vc.pdb
	-$(DEL_FILE) OurChat.ilk
	-$(DEL_FILE) OurChat.idb
distclean: debug-distclean release-distclean  FORCE
	-$(DEL_FILE) Makefile
	-$(DEL_FILE) .qmake.stash OurChat.pdb

debug-mocclean:
	@set MAKEFLAGS=$(MAKEFLAGS)
	$(MAKE) -f $(MAKEFILE).Debug mocclean
release-mocclean:
	@set MAKEFLAGS=$(MAKEFLAGS)
	$(MAKE) -f $(MAKEFILE).Release mocclean
mocclean: debug-mocclean release-mocclean

debug-mocables:
	@set MAKEFLAGS=$(MAKEFLAGS)
	$(MAKE) -f $(MAKEFILE).Debug mocables
release-mocables:
	@set MAKEFLAGS=$(MAKEFLAGS)
	$(MAKE) -f $(MAKEFILE).Release mocables
mocables: debug-mocables release-mocables

check: first

benchmark: first
FORCE:

$(MAKEFILE).Debug: Makefile
$(MAKEFILE).Release: Makefile
