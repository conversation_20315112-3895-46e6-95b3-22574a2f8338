<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>Client</class>
 <widget class="QWidget" name="Client">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>933</width>
    <height>657</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Client</string>
  </property>
  <property name="windowIcon">
   <iconset resource="src.qrc">
    <normaloff>:/pic/src/wechat2.png</normaloff>:/pic/src/wechat2.png</iconset>
  </property>
  <property name="styleSheet">
   <string notr="true">background-color: rgb(255, 255, 255);</string>
  </property>
  <widget class="QWidget" name="centerWidget" native="true">
   <property name="geometry">
    <rect>
     <x>0</x>
     <y>0</y>
     <width>933</width>
     <height>657</height>
    </rect>
   </property>
   <layout class="QGridLayout" name="gridLayout_4" columnstretch="0">
    <property name="leftMargin">
     <number>0</number>
    </property>
    <property name="topMargin">
     <number>0</number>
    </property>
    <property name="rightMargin">
     <number>0</number>
    </property>
    <property name="bottomMargin">
     <number>0</number>
    </property>
    <property name="spacing">
     <number>0</number>
    </property>
    <item row="0" column="0">
     <widget class="QWidget" name="widget_main" native="true">
      <widget class="QWidget" name="widget_side" native="true">
       <property name="geometry">
        <rect>
         <x>-2</x>
         <y>10</y>
         <width>80</width>
         <height>657</height>
        </rect>
       </property>
       <property name="minimumSize">
        <size>
         <width>80</width>
         <height>0</height>
        </size>
       </property>
       <property name="maximumSize">
        <size>
         <width>80</width>
         <height>16777215</height>
        </size>
       </property>
       <property name="styleSheet">
        <string notr="true">background-color: #0ED07B;
min-width:80;
max-width:80;</string>
       </property>
       <widget class="QPushButton" name="pushButton_msg_list">
        <property name="geometry">
         <rect>
          <x>20</x>
          <y>180</y>
          <width>40</width>
          <height>40</height>
         </rect>
        </property>
        <property name="minimumSize">
         <size>
          <width>40</width>
          <height>0</height>
         </size>
        </property>
        <property name="maximumSize">
         <size>
          <width>40</width>
          <height>16777215</height>
         </size>
        </property>
        <property name="cursor">
         <cursorShape>PointingHandCursor</cursorShape>
        </property>
        <property name="styleSheet">
         <string notr="true">QPushButton {

	border: none; /* no border for a flat push button */
	max-width:40;
	min-width:40;
}
</string>
        </property>
        <property name="text">
         <string/>
        </property>
        <property name="icon">
         <iconset resource="src.qrc">
          <normaloff>:/src/message.png</normaloff>
          <normalon>:/src/message1.png</normalon>:/src/message.png</iconset>
        </property>
        <property name="iconSize">
         <size>
          <width>40</width>
          <height>40</height>
         </size>
        </property>
        <property name="checkable">
         <bool>true</bool>
        </property>
        <property name="checked">
         <bool>false</bool>
        </property>
       </widget>
       <widget class="QPushButton" name="pushBtn_refresh">
        <property name="geometry">
         <rect>
          <x>20</x>
          <y>610</y>
          <width>42</width>
          <height>40</height>
         </rect>
        </property>
        <property name="minimumSize">
         <size>
          <width>42</width>
          <height>0</height>
         </size>
        </property>
        <property name="maximumSize">
         <size>
          <width>42</width>
          <height>2000</height>
         </size>
        </property>
        <property name="font">
         <font>
          <family>黑体</family>
          <pointsize>10</pointsize>
         </font>
        </property>
        <property name="cursor">
         <cursorShape>ArrowCursor</cursorShape>
        </property>
        <property name="styleSheet">
         <string notr="true">QPushButton{
	color: rgb(96,96,96);
	border-radius: 5px;	
	border-style: solid;
	background-color: rgb(245,245,245);

	border-right:1px solid #E5E5E5;
	border-top:1px solid #E5E5E5;
	border-left:1px solid #E5E5E5;
	border-bottom:1px solid #E5E5E5;

	max-width:40;
	min-width:40;

}

QPushButton:hover{
	color: rgb(255, 255, 255);
	background-color: rgb(18,150,17);
}

QPushButton:pressed {
	background-color: rgb(7,193,96);
}</string>
        </property>
        <property name="text">
         <string>刷新</string>
        </property>
       </widget>

       <widget class="QPushButton" name="pushButton_friend_list">
        <property name="geometry">
         <rect>
          <x>20</x>
          <y>250</y>
          <width>40</width>
          <height>40</height>
         </rect>
        </property>
        <property name="minimumSize">
         <size>
          <width>40</width>
          <height>0</height>
         </size>
        </property>
        <property name="maximumSize">
         <size>
          <width>40</width>
          <height>16777215</height>
         </size>
        </property>
        <property name="cursor">
         <cursorShape>PointingHandCursor</cursorShape>
        </property>
        <property name="mouseTracking">
         <bool>false</bool>
        </property>
        <property name="styleSheet">
         <string notr="true">QPushButton {

	border: none; /* no border for a flat push button */
	max-width:40;
	min-width:40;
}
</string>
        </property>
        <property name="text">
         <string/>
        </property>
        <property name="icon">
         <iconset resource="src.qrc">
          <normaloff>:/src/friend.png</normaloff>
          <normalon>:/src/friend1.png</normalon>:/src/friend.png</iconset>
        </property>
        <property name="iconSize">
         <size>
          <width>40</width>
          <height>40</height>
         </size>
        </property>
        <property name="checkable">
         <bool>true</bool>
        </property>
        <property name="checked">
         <bool>false</bool>
        </property>
       </widget>
       <widget class="QPushButton" name="pushButton_group_list">
        <property name="geometry">
         <rect>
          <x>20</x>
          <y>320</y>
          <width>40</width>
          <height>40</height>
         </rect>
        </property>
        <property name="minimumSize">
         <size>
          <width>40</width>
          <height>0</height>
         </size>
        </property>
        <property name="maximumSize">
         <size>
          <width>40</width>
          <height>16777215</height>
         </size>
        </property>
        <property name="cursor">
         <cursorShape>PointingHandCursor</cursorShape>
        </property>
        <property name="styleSheet">
         <string notr="true">border:none;
	max-width:40;
	min-width:40;</string>
        </property>
        <property name="text">
         <string/>
        </property>
        <property name="icon">
         <iconset resource="src.qrc">
          <normaloff>:/src/group.png</normaloff>
          <normalon>:/src/group1.png</normalon>:/src/group.png</iconset>
        </property>
        <property name="iconSize">
         <size>
          <width>40</width>
          <height>40</height>
         </size>
        </property>
        <property name="checkable">
         <bool>true</bool>
        </property>
        <property name="checked">
         <bool>false</bool>
        </property>
       </widget>
       <widget class="QPushButton" name="pushButton_system_msg">
        <property name="geometry">
         <rect>
          <x>20</x>
          <y>390</y>
          <width>40</width>
          <height>40</height>
         </rect>
        </property>
        <property name="minimumSize">
         <size>
          <width>40</width>
          <height>0</height>
         </size>
        </property>
        <property name="maximumSize">
         <size>
          <width>40</width>
          <height>16777215</height>
         </size>
        </property>
        <property name="cursor">
         <cursorShape>PointingHandCursor</cursorShape>
        </property>
        <property name="styleSheet">
         <string notr="true">QPushButton {

	border: none; /* no border for a flat push button */
	max-width:40;
	min-width:40;
}
</string>
        </property>
        <property name="text">
         <string/>
        </property>
        <property name="icon">
         <iconset resource="src.qrc">
          <normaloff>:/src/notification.png</normaloff>:/src/notification.png</iconset>
        </property>
        <property name="iconSize">
         <size>
          <width>40</width>
          <height>40</height>
         </size>
        </property>
        <property name="checkable">
         <bool>false</bool>
        </property>
        <property name="checked">
         <bool>false</bool>
        </property>
       </widget>
       <widget class="FriendIconLabel" name="label_icon">
        <property name="geometry">
         <rect>
          <x>0</x>
          <y>0</y>
          <width>80</width>
          <height>80</height>
         </rect>
        </property>
        <property name="minimumSize">
         <size>
          <width>80</width>
          <height>80</height>
         </size>
        </property>
        <property name="maximumSize">
         <size>
          <width>80</width>
          <height>80</height>
         </size>
        </property>
        <property name="styleSheet">
         <string notr="true">	max-width:80;
	min-width:80;
background-color:transparent;</string>
        </property>
        <property name="text">
         <string/>
        </property>
        <property name="scaledContents">
         <bool>true</bool>
        </property>
       </widget>
       <widget class="QPushButton" name="pushButton_icon">
        <property name="geometry">
         <rect>
          <x>0</x>
          <y>0</y>
          <width>80</width>
          <height>80</height>
         </rect>
        </property>
        <property name="sizePolicy">
         <sizepolicy hsizetype="Minimum" vsizetype="Fixed">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="minimumSize">
         <size>
          <width>80</width>
          <height>80</height>
         </size>
        </property>
        <property name="maximumSize">
         <size>
          <width>80</width>
          <height>80</height>
         </size>
        </property>
        <property name="font">
         <font>
          <pointsize>9</pointsize>
         </font>
        </property>
        <property name="cursor">
         <cursorShape>PointingHandCursor</cursorShape>
        </property>
        <property name="styleSheet">
         <string notr="true">max-width:80;
min-width:80;
border:none;
background-color:transparent;</string>
        </property>
        <property name="text">
         <string/>
        </property>
       </widget>
      </widget>
      <widget class="QWidget" name="widget_list" native="true">
       <property name="geometry">
        <rect>
         <x>78</x>
         <y>10</y>
         <width>202</width>
         <height>94</height>
        </rect>
       </property>
       <layout class="QGridLayout" name="gridLayout_2">
        <property name="spacing">
         <number>0</number>
        </property>
        <item row="1" column="0">
         <widget class="QWidget" name="widget_6" native="true">
          <property name="minimumSize">
           <size>
            <width>200</width>
            <height>60</height>
           </size>
          </property>
          <property name="maximumSize">
           <size>
            <width>999999</width>
            <height>999999</height>
           </size>
          </property>
          <property name="styleSheet">
           <string notr="true">background-color: rgb(247,247,247);
border-right:1px solid #D6D6D6;</string>
          </property>
          <layout class="QHBoxLayout" name="horizontalLayout">
           <item>
            <widget class="QLineEdit" name="lineEdit">
             <property name="sizePolicy">
              <sizepolicy hsizetype="Expanding" vsizetype="Preferred">
               <horstretch>0</horstretch>
               <verstretch>0</verstretch>
              </sizepolicy>
             </property>
             <property name="maximumSize">
              <size>
               <width>16777215</width>
               <height>20</height>
              </size>
             </property>
             <property name="styleSheet">
              <string notr="true">QLineEdit
{
	background-color: rgb(226,226,226);
	border: none; /* no border for a flat push button */
}
QLineEdit
{
	background-color: rgb(226,226,226);
	border: none; /* no border for a flat push button */
}
</string>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QPushButton" name="pushButton_addFriend">
             <property name="minimumSize">
              <size>
               <width>20</width>
               <height>20</height>
              </size>
             </property>
             <property name="maximumSize">
              <size>
               <width>20</width>
               <height>20</height>
              </size>
             </property>
             <property name="toolTip">
              <string>添加好友</string>
             </property>
             <property name="styleSheet">
              <string notr="true">QPushButton {
	border: none; /* no border for a flat push button */
	image: url(:/pic/src/tianjia.png);
	background-color: rgb(226,226,226);
	min-width: 20px;
	max-width: 20px;
	min-height: 20px;
	max-height: 20px;
}
QPushButton::hover {
	background-color: rgb(209,209,209);
	image: url(:/pic/src/tianjia2.png);
}
QPushButton:pressed {
	image: url(:/pic/src/tianjia.png);
}</string>
             </property>
             <property name="text">
              <string/>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QPushButton" name="pushButton_createGroup">
             <property name="minimumSize">
              <size>
               <width>20</width>
               <height>20</height>
              </size>
             </property>
             <property name="maximumSize">
              <size>
               <width>20</width>
               <height>20</height>
              </size>
             </property>
             <property name="toolTip">
              <string>创建群聊</string>
             </property>
             <property name="styleSheet">
              <string notr="true">QPushButton {
	border: none; /* no border for a flat push button */
	image: url(:/pic/src/group.png);
	background-color: rgb(226,226,226);
	min-width: 20px;
	max-width: 20px;
	min-height: 20px;
	max-height: 20px;
}
QPushButton::hover {
	background-color: rgb(209,209,209);
	image: url(:/pic/src/group1.png);
}
QPushButton:pressed {
	image: url(:/pic/src/group.png);
}</string>
             </property>
             <property name="text">
              <string/>
             </property>
            </widget>
           </item>
          </layout>
         </widget>
        </item>
        <item row="2" column="0">
         <widget class="QStackedWidget" name="stackedWidget_list">
          <widget class="QWidget" name="page_5"/>
          <widget class="QWidget" name="page_6"/>
         </widget>
        </item>
       </layout>
      </widget>
      <widget class="QWidget" name="widget_chatting" native="true">
       <property name="geometry">
        <rect>
         <x>330</x>
         <y>10</y>
         <width>601</width>
         <height>657</height>
        </rect>
       </property>
       <layout class="QGridLayout" name="gridLayout">
        <property name="spacing">
         <number>0</number>
        </property>
        <item row="4" column="0">
         <widget class="QWidget" name="widget_3" native="true">
          <property name="minimumSize">
           <size>
            <width>0</width>
            <height>35</height>
           </size>
          </property>
          <property name="maximumSize">
           <size>
            <width>16777215</width>
            <height>35</height>
           </size>
          </property>
          <property name="styleSheet">
           <string notr="true">background-color: rgb(255, 255, 255);</string>
          </property>
          <layout class="QGridLayout" name="gridLayout_10">
           <property name="leftMargin">
            <number>0</number>
           </property>
           <property name="topMargin">
            <number>0</number>
           </property>
           <property name="rightMargin">
            <number>0</number>
           </property>
           <property name="bottomMargin">
            <number>0</number>
           </property>
           <property name="spacing">
            <number>0</number>
           </property>
           <item row="0" column="2">
            <widget class="QPushButton" name="pushBtn_send">
             <property name="minimumSize">
              <size>
               <width>70</width>
               <height>25</height>
              </size>
             </property>
             <property name="maximumSize">
              <size>
               <width>70</width>
               <height>25</height>
              </size>
             </property>
             <property name="font">
              <font>
               <family>黑体</family>
               <pointsize>10</pointsize>
              </font>
             </property>
             <property name="styleSheet">
              <string notr="true">QPushButton{
	color: rgb(96,96,96);
	border-radius: 5px;	
	border-style: solid;
	background-color: rgb(245,245,245);

	border-right:1px solid #E5E5E5;
	border-top:1px solid #E5E5E5;
	border-left:1px solid #E5E5E5;
	border-bottom:1px solid #E5E5E5;
}

QPushButton:hover{
	color: rgb(255, 255, 255);
	background-color: rgb(18,150,17);
}

QPushButton:pressed {
	background-color: rgb(7,193,96);
}</string>
             </property>
             <property name="text">
              <string>发送</string>
             </property>
            </widget>
           </item>
           <item row="0" column="1">
            <spacer name="horizontalSpacer">
             <property name="orientation">
              <enum>Qt::Horizontal</enum>
             </property>
             <property name="sizeHint" stdset="0">
              <size>
               <width>40</width>
               <height>20</height>
              </size>
             </property>
            </spacer>
           </item>
          </layout>
         </widget>
        </item>
        <item row="1" column="0">
         <widget class="QWidget" name="widget_chatWindow" native="true">
          <property name="layoutDirection">
           <enum>Qt::LeftToRight</enum>
          </property>
          <property name="autoFillBackground">
           <bool>false</bool>
          </property>
          <property name="styleSheet">
           <string notr="true"/>
          </property>
          <layout class="QGridLayout" name="gridLayout_3">
           <property name="leftMargin">
            <number>0</number>
           </property>
           <property name="topMargin">
            <number>0</number>
           </property>
           <property name="rightMargin">
            <number>0</number>
           </property>
           <property name="bottomMargin">
            <number>0</number>
           </property>
           <property name="spacing">
            <number>0</number>
           </property>
           <item row="0" column="0">
            <widget class="QStackedWidget" name="stackedWidget">
             <property name="sizePolicy">
              <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
               <horstretch>0</horstretch>
               <verstretch>0</verstretch>
              </sizepolicy>
             </property>
             <property name="autoFillBackground">
              <bool>false</bool>
             </property>
             <property name="styleSheet">
              <string notr="true">background-color: rgb(255, 255, 255);
border-color: rgb(199, 200, 194);</string>
             </property>
             <property name="frameShadow">
              <enum>QFrame::Plain</enum>
             </property>
             <property name="lineWidth">
              <number>10</number>
             </property>
             <property name="currentIndex">
              <number>1</number>
             </property>
             <widget class="QWidget" name="page">
              <property name="autoFillBackground">
               <bool>false</bool>
              </property>
              <property name="styleSheet">
               <string notr="true">image: url(:/src/chatbg.png);
background-color:#F5F5F5;</string>
              </property>
             </widget>
             <widget class="QWidget" name="page_2"/>
            </widget>
           </item>
          </layout>
         </widget>
        </item>
        <item row="0" column="0">
         <widget class="QWidget" name="widget_2" native="true">
          <property name="minimumSize">
           <size>
            <width>0</width>
            <height>0</height>
           </size>
          </property>
          <property name="maximumSize">
           <size>
            <width>16777215</width>
            <height>16777215</height>
           </size>
          </property>
          <property name="styleSheet">
           <string notr="true">background-color: rgb(245,245,245);</string>
          </property>
          <layout class="QGridLayout" name="gridLayout_8">
           <property name="leftMargin">
            <number>0</number>
           </property>
           <property name="topMargin">
            <number>0</number>
           </property>
           <property name="rightMargin">
            <number>0</number>
           </property>
           <property name="bottomMargin">
            <number>0</number>
           </property>
           <property name="spacing">
            <number>0</number>
           </property>
           <item row="0" column="0">
            <layout class="QGridLayout" name="gridLayout_6" columnstretch="1,0,0,0">
             <property name="spacing">
              <number>0</number>
             </property>
             <item row="0" column="1">
              <widget class="QPushButton" name="pushBtn_hide">
               <property name="minimumSize">
                <size>
                 <width>30</width>
                 <height>25</height>
                </size>
               </property>
               <property name="maximumSize">
                <size>
                 <width>30</width>
                 <height>25</height>
                </size>
               </property>
               <property name="styleSheet">
                <string notr="true">QPushButton {
	border: none; /* no border for a flat push button */
	image: url(:/pic/src/min.png); /* 最小化 */
	min-width: 30px;
	max-width: 30px;
	min-height: 25px;
	max-height: 25px;
}
QPushButton::hover {
	background-color: rgba(166,166,166, 50); 
}</string>
               </property>
               <property name="text">
                <string/>
               </property>
              </widget>
             </item>
             <item row="0" column="2">
              <widget class="QPushButton" name="pushBtn_max">
               <property name="minimumSize">
                <size>
                 <width>30</width>
                 <height>25</height>
                </size>
               </property>
               <property name="maximumSize">
                <size>
                 <width>30</width>
                 <height>25</height>
                </size>
               </property>
               <property name="styleSheet">
                <string notr="true">QPushButton {
	border: none; /* no border for a flat push button */
	image: url(:/pic/src/max.png); /* 最小化 */
	min-width: 30px;
	max-width: 30px;
	min-height: 25px;
	max-height: 25px;
}
QPushButton::hover {
	background-color: rgba(166,166,166, 50); 
}</string>
               </property>
               <property name="text">
                <string/>
               </property>
              </widget>
             </item>
             <item row="0" column="3">
              <widget class="QPushButton" name="pushBtn_close">
               <property name="minimumSize">
                <size>
                 <width>30</width>
                 <height>25</height>
                </size>
               </property>
               <property name="maximumSize">
                <size>
                 <width>30</width>
                 <height>25</height>
                </size>
               </property>
               <property name="styleSheet">
                <string notr="true">QPushButton {
	border: none; /* no border for a flat push button */
	image: url(:/pic/src/close.png); /* 最小化 */
	min-width: 30px;
	max-width: 30px;
	min-height: 25px;
	max-height: 25px;
}
QPushButton::hover {
	background-color: #f57575; 
}</string>
               </property>
               <property name="text">
                <string/>
               </property>
              </widget>
             </item>
             <item row="0" column="0">
              <widget class="QWidget" name="widget_7" native="true"/>
             </item>
            </layout>
           </item>
           <item row="1" column="0">
            <layout class="QGridLayout" name="gridLayout_7" columnstretch="0,0,0,0,0">
             <item row="0" column="1">
              <widget class="QLabel" name="label_info">
               <property name="font">
                <font>
                 <family>新宋体</family>
                 <pointsize>14</pointsize>
                </font>
               </property>
               <property name="text">
                <string>聊天信息</string>
               </property>
              </widget>
             </item>
             <item row="0" column="2">
              <widget class="QWidget" name="widget_8" native="true"/>
             </item>
             <item row="0" column="4">
              <widget class="QWidget" name="widget_16" native="true">
               <property name="minimumSize">
                <size>
                 <width>10</width>
                 <height>0</height>
                </size>
               </property>
               <property name="maximumSize">
                <size>
                 <width>10</width>
                 <height>16777215</height>
                </size>
               </property>
              </widget>
             </item>
             <item row="0" column="0">
              <widget class="QWidget" name="widget_15" native="true">
               <property name="minimumSize">
                <size>
                 <width>20</width>
                 <height>0</height>
                </size>
               </property>
               <property name="maximumSize">
                <size>
                 <width>20</width>
                 <height>16777215</height>
                </size>
               </property>
              </widget>
             </item>
             <item row="0" column="3">
              <widget class="QPushButton" name="pushButton">
               <property name="styleSheet">
                <string notr="true">QPushButton {
	border: none; /* no border for a flat push button */
	image: url(:/src/more.png); 
	min-width: 30px;
	max-width: 30px;
	min-height: 20px;
	max-height: 20px;
}
QPushButton::hover {
	image: url(:/src/more2.png); 
}
QPushButton:pressed {
	image: url(:/src/more.png); 
}</string>
               </property>
               <property name="text">
                <string/>
               </property>
              </widget>
             </item>
            </layout>
           </item>
          </layout>
         </widget>
        </item>
        <item row="3" column="0">
         <widget class="QWidget" name="widget_send" native="true">
          <property name="minimumSize">
           <size>
            <width>0</width>
            <height>0</height>
           </size>
          </property>
          <property name="maximumSize">
           <size>
            <width>16777215</width>
            <height>16777215</height>
           </size>
          </property>
          <layout class="QGridLayout" name="gridLayout_14">
           <property name="leftMargin">
            <number>0</number>
           </property>
           <property name="topMargin">
            <number>0</number>
           </property>
           <property name="rightMargin">
            <number>0</number>
           </property>
           <property name="bottomMargin">
            <number>0</number>
           </property>
           <property name="spacing">
            <number>0</number>
           </property>
           <item row="0" column="0">
            <widget class="QWidget" name="widget_4" native="true">
             <property name="minimumSize">
              <size>
               <width>0</width>
               <height>30</height>
              </size>
             </property>
             <property name="maximumSize">
              <size>
               <width>16777215</width>
               <height>30</height>
              </size>
             </property>
             <property name="styleSheet">
              <string notr="true">background-color: rgb(200, 196, 188);</string>
             </property>
             <widget class="QPushButton" name="pushButton_emoj">
              <property name="geometry">
               <rect>
                <x>20</x>
                <y>6</y>
                <width>20</width>
                <height>20</height>
               </rect>
              </property>
              <property name="styleSheet">
               <string notr="true">QPushButton {
	border: none; /* no border for a flat push button */
	image: url(:/pic/src/emoj.png); 
	min-width: 20px;
	max-width: 20px;
	min-height: 20px;
	max-height: 20px;
}
QPushButton::hover {
	image: url(:/pic/src/emoj2.png); 
}
QPushButton:pressed {
	image: url(:/pic/src/emoj.png); 
}</string>
              </property>
              <property name="text">
               <string/>
              </property>
             </widget>
             <widget class="QPushButton" name="pushButton_screenshot">
              <property name="geometry">
               <rect>
                <x>120</x>
                <y>6</y>
                <width>20</width>
                <height>20</height>
               </rect>
              </property>
              <property name="styleSheet">
               <string notr="true">QPushButton {
	border: none; /* no border for a flat push button */
	image: url(:/pic/src/screenshot.png); 
	min-width: 20px;
	max-width: 20px;
	min-height: 20px;
	max-height: 20px;
}
QPushButton::hover {
	image: url(:/pic/src/screenshot2.png); 
}
QPushButton:pressed {
	image: url(:/pic/src/screenshot.png); 
}</string>
              </property>
              <property name="text">
               <string/>
              </property>
             </widget>
             <widget class="QPushButton" name="pushButton_chat">
              <property name="geometry">
               <rect>
                <x>170</x>
                <y>6</y>
                <width>20</width>
                <height>20</height>
               </rect>
              </property>
              <property name="styleSheet">
               <string notr="true">QPushButton {
	border: none; /* no border for a flat push button */
	image: url(:/pic/src/chat.png); 
	min-width: 20px;
	max-width: 20px;
	min-height: 20px;
	max-height: 20px;
}
QPushButton::hover {
	image: url(:/pic/src/chat2.png); 
}
QPushButton:pressed {
	image: url(:/pic/src/chat.png); 
}</string>
              </property>
              <property name="text">
               <string/>
              </property>
             </widget>

             <widget class="QPushButton" name="pushButton_image">
              <property name="geometry">
               <rect>
                <x>70</x>
                <y>6</y>
                <width>20</width>
                <height>20</height>
               </rect>
              </property>
              <property name="styleSheet">
               <string notr="true">QPushButton {
	border: none; /* no border for a flat push button */
	image: url(:/pic/src/image.png); 
	min-width: 20px;
	max-width: 20px;
	min-height: 20px;
	max-height: 20px;
}
QPushButton::hover {
	image: url(:/pic/src/image1.png); 
}
QPushButton:pressed {
	image: url(:/pic/src/image.png); 
}</string>
              </property>
              <property name="text">
               <string/>
              </property>
             </widget>
            </widget>
           </item>
           <item row="4" column="0">
            <widget class="SendTextEdit" name="textEdit_send">
             <property name="minimumSize">
              <size>
               <width>0</width>
               <height>0</height>
              </size>
             </property>
             <property name="maximumSize">
              <size>
               <width>16777215</width>
               <height>16777215</height>
              </size>
             </property>
             <property name="font">
              <font>
               <family>新宋体</family>
               <pointsize>12</pointsize>
              </font>
             </property>
             <property name="styleSheet">
              <string notr="true">background-color: rgb(255, 255, 255);
border: none; /* no border for a flat push button */
</string>
             </property>
             <property name="verticalScrollBarPolicy">
              <enum>Qt::ScrollBarAlwaysOff</enum>
             </property>
             <property name="horizontalScrollBarPolicy">
              <enum>Qt::ScrollBarAlwaysOff</enum>
             </property>
            </widget>
           </item>
          </layout>
         </widget>
        </item>
       </layout>
      </widget>
     </widget>
    </item>
   </layout>
  </widget>
 </widget>
 <customwidgets>
  <customwidget>
   <class>SendTextEdit</class>
   <extends>QTextEdit</extends>
   <header location="global">sendtextedit.h</header>
  </customwidget>
  <customwidget>
   <class>FriendIconLabel</class>
   <extends>QLabel</extends>
   <header location="global">friendiconlabel.h</header>
  </customwidget>
 </customwidgets>
 <resources>
  <include location="src.qrc"/>
 </resources>
 <connections/>
</ui>
