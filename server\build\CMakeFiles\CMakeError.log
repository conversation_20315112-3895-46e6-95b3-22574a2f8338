Performing C SOURCE FILE Test CMAKE_HAVE_LIBC_PTHREAD failed with the following output:
Change Dir: /home/<USER>/OurChat/server/build/CMakeFiles/CMakeTmp

Run Build Command(s):/usr/bin/gmake -f Makefile cmTC_b4cd6/fast && /usr/bin/gmake  -f CMakeFiles/cmTC_b4cd6.dir/build.make CMakeFiles/cmTC_b4cd6.dir/build
gmake[1]: Entering directory `/home/<USER>/OurChat/server/build/CMakeFiles/CMakeTmp'
Building C object CMakeFiles/cmTC_b4cd6.dir/src.c.o
/opt/rh/devtoolset-8/root/usr/bin/cc -DCMAKE_HAVE_LIBC_PTHREAD   -o CMakeFiles/cmTC_b4cd6.dir/src.c.o -c /home/<USER>/OurChat/server/build/CMakeFiles/CMakeTmp/src.c
Linking C executable cmTC_b4cd6
/usr/local/bin/cmake -E cmake_link_script CMakeFiles/cmTC_b4cd6.dir/link.txt --verbose=1
/opt/rh/devtoolset-8/root/usr/bin/cc CMakeFiles/cmTC_b4cd6.dir/src.c.o -o cmTC_b4cd6 
CMakeFiles/cmTC_b4cd6.dir/src.c.o: In function `main':
src.c:(.text+0x2d): undefined reference to `pthread_create'
src.c:(.text+0x39): undefined reference to `pthread_detach'
src.c:(.text+0x45): undefined reference to `pthread_cancel'
src.c:(.text+0x56): undefined reference to `pthread_join'
src.c:(.text+0x6a): undefined reference to `pthread_atfork'
collect2: error: ld returned 1 exit status
gmake[1]: *** [cmTC_b4cd6] Error 1
gmake[1]: Leaving directory `/home/<USER>/OurChat/server/build/CMakeFiles/CMakeTmp'
gmake: *** [cmTC_b4cd6/fast] Error 2


Source file was:
#include <pthread.h>

static void* test_func(void* data)
{
  return data;
}

int main(void)
{
  pthread_t thread;
  pthread_create(&thread, NULL, test_func, NULL);
  pthread_detach(thread);
  pthread_cancel(thread);
  pthread_join(thread, NULL);
  pthread_atfork(NULL, NULL, NULL);
  pthread_exit(NULL);

  return 0;
}

