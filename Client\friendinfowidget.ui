<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>FriendInfoWidget</class>
 <widget class="QWidget" name="FriendInfoWidget">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>422</width>
    <height>229</height>
   </rect>
  </property>
  <property name="font">
   <font>
    <pointsize>9</pointsize>
   </font>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <widget class="QLabel" name="label_name">
   <property name="geometry">
    <rect>
     <x>25</x>
     <y>80</y>
     <width>111</width>
     <height>41</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <pointsize>20</pointsize>
    </font>
   </property>
   <property name="cursor">
    <cursorShape>IBeamCursor</cursorShape>
   </property>
   <property name="styleSheet">
    <string notr="true">color:#ffffff;</string>
   </property>
   <property name="text">
    <string>name</string>
   </property>
   <property name="textFormat">
    <enum>Qt::MarkdownText</enum>
   </property>
   <property name="textInteractionFlags">
    <set>Qt::LinksAccessibleByMouse|Qt::TextSelectableByMouse</set>
   </property>
  </widget>
  <widget class="QTextEdit" name="textEdit_sig">
   <property name="geometry">
    <rect>
     <x>30</x>
     <y>160</y>
     <width>101</width>
     <height>31</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="font">
    <font>
     <pointsize>11</pointsize>
    </font>
   </property>
   <property name="cursor" stdset="0">
    <cursorShape>IBeamCursor</cursorShape>
   </property>
   <property name="styleSheet">
    <string notr="true">background:transparent;border-width:0;border-style:outset;color:#ffffff;</string>
   </property>
   <property name="readOnly">
    <bool>true</bool>
   </property>
  </widget>
  <widget class="QLabel" name="label_account">
   <property name="geometry">
    <rect>
     <x>25</x>
     <y>40</y>
     <width>121</width>
     <height>41</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <pointsize>20</pointsize>
    </font>
   </property>
   <property name="cursor">
    <cursorShape>IBeamCursor</cursorShape>
   </property>
   <property name="styleSheet">
    <string notr="true">color:#ffffff;</string>
   </property>
   <property name="text">
    <string>account</string>
   </property>
   <property name="textInteractionFlags">
    <set>Qt::LinksAccessibleByMouse|Qt::TextSelectableByMouse</set>
   </property>
  </widget>
  <widget class="QLabel" name="label_bg">
   <property name="geometry">
    <rect>
     <x>0</x>
     <y>0</y>
     <width>422</width>
     <height>229</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <pointsize>9</pointsize>
    </font>
   </property>
   <property name="styleSheet">
    <string notr="true"/>
   </property>
   <property name="text">
    <string/>
   </property>
   <property name="pixmap">
    <pixmap resource="src.qrc">:/src/friendItemBg.png</pixmap>
   </property>
   <property name="scaledContents">
    <bool>true</bool>
   </property>
   <property name="textInteractionFlags">
    <set>Qt::NoTextInteraction</set>
   </property>
  </widget>
  <zorder>label_bg</zorder>
  <zorder>label_name</zorder>
  <zorder>textEdit_sig</zorder>
  <zorder>label_account</zorder>
 </widget>
 <resources>
  <include location="src.qrc"/>
 </resources>
 <connections/>
</ui>
