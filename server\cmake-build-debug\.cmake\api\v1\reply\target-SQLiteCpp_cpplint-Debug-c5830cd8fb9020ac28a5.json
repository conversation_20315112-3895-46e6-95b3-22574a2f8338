{"backtrace": 1, "backtraceGraph": {"commands": ["add_custom_target"], "files": ["thirdparty/sQLitecpp/CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 354, "parent": 0}]}, "id": "SQLiteCpp_cpplint::@7bfbb0359d215dd9211f", "name": "SQLiteCpp_cpplint", "paths": {"build": "thirdparty/SQLiteCpp", "source": "thirdparty/sQLitecpp"}, "sourceGroups": [{"name": "", "sourceIndexes": [0]}, {"name": "CMake Rules", "sourceIndexes": [1]}], "sources": [{"backtrace": 1, "isGenerated": true, "path": "cmake-build-debug/thirdparty/SQLiteCpp/CMakeFiles/SQLiteCpp_cpplint", "sourceGroupIndex": 0}, {"backtrace": 0, "isGenerated": true, "path": "cmake-build-debug/thirdparty/SQLiteCpp/CMakeFiles/SQLiteCpp_cpplint.rule", "sourceGroupIndex": 1}], "type": "UTILITY"}