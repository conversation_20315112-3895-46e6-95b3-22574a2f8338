{"cmake": {"generator": {"multiConfig": false, "name": "Ninja"}, "paths": {"cmake": "D:/Program Files/JetBrains/CLion 2024.1.2/bin/cmake/win/x64/bin/cmake.exe", "cpack": "D:/Program Files/JetBrains/CLion 2024.1.2/bin/cmake/win/x64/bin/cpack.exe", "ctest": "D:/Program Files/JetBrains/CLion 2024.1.2/bin/cmake/win/x64/bin/ctest.exe", "root": "D:/Program Files/JetBrains/CLion 2024.1.2/bin/cmake/win/x64/share/cmake-3.28"}, "version": {"isDirty": false, "major": 3, "minor": 28, "patch": 1, "string": "3.28.1", "suffix": ""}}, "objects": [{"jsonFile": "codemodel-v2-cf5c53b817fb32e5c15e.json", "kind": "codemodel", "version": {"major": 2, "minor": 6}}, {"jsonFile": "cache-v2-4b01eca9a8db370d276e.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-e4c720d44e482c29e294.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}, {"jsonFile": "toolchains-v1-9f0584aace227fac2a49.json", "kind": "toolchains", "version": {"major": 1, "minor": 0}}], "reply": {"cache-v2": {"jsonFile": "cache-v2-4b01eca9a8db370d276e.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, "cmakeFiles-v1": {"jsonFile": "cmakeFiles-v1-e4c720d44e482c29e294.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}, "codemodel-v2": {"jsonFile": "codemodel-v2-cf5c53b817fb32e5c15e.json", "kind": "codemodel", "version": {"major": 2, "minor": 6}}, "toolchains-v1": {"jsonFile": "toolchains-v1-9f0584aace227fac2a49.json", "kind": "toolchains", "version": {"major": 1, "minor": 0}}}}