<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>SelfInfoWidget</class>
 <widget class="QWidget" name="SelfInfoWidget">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>953</width>
    <height>675</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>我的资料</string>
  </property>
  <property name="windowIcon">
   <iconset>
    <normaloff>:/src/个人中心绿.png</normaloff>:/src/个人中心绿.png</iconset>
  </property>
  <widget class="QWidget" name="horizontalLayoutWidget">
   <property name="geometry">
    <rect>
     <x>0</x>
     <y>0</y>
     <width>951</width>
     <height>671</height>
    </rect>
   </property>
   <layout class="QHBoxLayout" name="horizontalLayout">
    <property name="spacing">
     <number>0</number>
    </property>
    <item>
     <layout class="QGridLayout" name="gridLayout">
      <property name="spacing">
       <number>0</number>
      </property>
      <item row="1" column="0">
       <widget class="QWidget" name="widget_2" native="true">
        <widget class="QLabel" name="label">
         <property name="geometry">
          <rect>
           <x>0</x>
           <y>-40</y>
           <width>501</width>
           <height>491</height>
          </rect>
         </property>
         <property name="text">
          <string/>
         </property>
         <property name="pixmap">
          <pixmap resource="src.qrc">:/src/bg1.png</pixmap>
         </property>
         <property name="scaledContents">
          <bool>true</bool>
         </property>
        </widget>
        <widget class="QPushButton" name="pushButton">
         <property name="geometry">
          <rect>
           <x>380</x>
           <y>363</y>
           <width>80</width>
           <height>80</height>
          </rect>
         </property>
         <property name="cursor">
          <cursorShape>PointingHandCursor</cursorShape>
         </property>
         <property name="focusPolicy">
          <enum>Qt::NoFocus</enum>
         </property>
         <property name="styleSheet">
          <string notr="true">border:0</string>
         </property>
         <property name="text">
          <string/>
         </property>
         <property name="icon">
          <iconset resource="src.qrc">
           <normaloff>:/src/dz2.png</normaloff>
           <normalon>:/src/dz1.png</normalon>:/src/dz2.png</iconset>
         </property>
         <property name="iconSize">
          <size>
           <width>80</width>
           <height>80</height>
          </size>
         </property>
         <property name="checkable">
          <bool>true</bool>
         </property>
        </widget>
        <widget class="QWidget" name="widget_3" native="true">
         <property name="geometry">
          <rect>
           <x>0</x>
           <y>450</y>
           <width>473</width>
           <height>217</height>
          </rect>
         </property>
         <property name="styleSheet">
          <string notr="true">background-color:#6F8E92</string>
         </property>
        </widget>
       </widget>
      </item>
     </layout>
    </item>
    <item>
     <widget class="QWidget" name="widget" native="true">
      <property name="styleSheet">
       <string notr="true">background-color: rgb(255, 255, 255);</string>
      </property>
      <widget class="QPushButton" name="pushBtn_hide">
       <property name="geometry">
        <rect>
         <x>390</x>
         <y>0</y>
         <width>34</width>
         <height>40</height>
        </rect>
       </property>
       <property name="styleSheet">
        <string notr="true">QPushButton {
	border: none; /* no border for a flat push button */
	image: url(:/src/min.png); /* 最小化 */
	min-width: 34px;
	max-width: 34px;
	min-height: 40px;
	max-height: 40px;
}
QPushButton::hover {
	background-color: rgba(166,166,166, 50); 
}</string>
       </property>
       <property name="text">
        <string/>
       </property>
       <property name="icon">
        <iconset resource="src.qrc">
         <normaloff>:/pic/src/min.png</normaloff>:/pic/src/min.png</iconset>
       </property>
       <property name="iconSize">
        <size>
         <width>40</width>
         <height>40</height>
        </size>
       </property>
      </widget>
      <widget class="QPushButton" name="pushBtn_close">
       <property name="geometry">
        <rect>
         <x>430</x>
         <y>0</y>
         <width>34</width>
         <height>40</height>
        </rect>
       </property>
       <property name="styleSheet">
        <string notr="true">QPushButton {
	border: none; /* no border for a flat push button */
	image: url(:/src/close.png); /* 最小化 */
	min-width: 34px;
	max-width: 34px;
	min-height: 40px;
	max-height: 40px;
}
QPushButton::hover {
	background-color: #f57575; 
}</string>
       </property>
       <property name="text">
        <string/>
       </property>
       <property name="icon">
        <iconset resource="src.qrc">
         <normaloff>:/pic/src/close.png</normaloff>:/pic/src/close.png</iconset>
       </property>
       <property name="iconSize">
        <size>
         <width>40</width>
         <height>40</height>
        </size>
       </property>
      </widget>
     </widget>
    </item>
   </layout>
  </widget>
 </widget>
 <resources>
  <include location="src.qrc"/>
 </resources>
 <connections/>
</ui>
