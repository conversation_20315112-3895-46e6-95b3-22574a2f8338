{"backtraceGraph": {"commands": ["install"], "files": ["thirdparty/sQLitecpp/CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 324, "parent": 0}, {"command": 0, "file": 0, "line": 329, "parent": 0}, {"command": 0, "file": 0, "line": 330, "parent": 0}, {"command": 0, "file": 0, "line": 331, "parent": 0}, {"command": 0, "file": 0, "line": 342, "parent": 0}]}, "installers": [{"backtrace": 1, "component": "libraries", "destination": "lib", "paths": ["thirdparty/SQLiteCpp/libSQLiteCpp.a"], "targetId": "SQLiteCpp::@7bfbb0359d215dd9211f", "targetIndex": 0, "type": "target"}, {"backtrace": 2, "component": "headers", "destination": "include", "paths": [{"from": "thirdparty/sQLitecpp/include", "to": "."}], "type": "directory"}, {"backtrace": 3, "component": "Unspecified", "destination": "lib/cmake/SQLiteCpp", "exportName": "SQLiteCppTargets", "exportTargets": [{"id": "sqlite3::@741eb9df4aa093c9dadc", "index": 3}, {"id": "SQLiteCpp::@7bfbb0359d215dd9211f", "index": 0}], "paths": ["thirdparty/SQLiteCpp/CMakeFiles/Export/31d559fcedc0800e90eb93f071f3b915/SQLiteCppTargets.cmake"], "type": "export"}, {"backtrace": 4, "component": "Unspecified", "destination": "share/SQLiteCpp", "paths": ["thirdparty/sQLitecpp/package.xml"], "type": "file"}, {"backtrace": 5, "component": "Unspecified", "destination": "lib/cmake/SQLiteCpp", "paths": ["cmake-build-debug/thirdparty/SQLiteCpp/cmake/SQLiteCppConfig.cmake", "cmake-build-debug/thirdparty/SQLiteCpp/cmake/SQLiteCppConfigVersion.cmake"], "type": "file"}], "paths": {"build": "thirdparty/SQLiteCpp", "source": "thirdparty/sQLitecpp"}}