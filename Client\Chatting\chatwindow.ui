<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>ChatWindow</class>
 <widget class="QWidget" name="ChatWindow">
  <property name="enabled">
   <bool>true</bool>
  </property>
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>620</width>
    <height>450</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <layout class="QGridLayout" name="gridLayout">
   <item row="0" column="0">
    <widget class="SendTextEdit" name="textEdit">
     <property name="enabled">
      <bool>true</bool>
     </property>
     <property name="styleSheet">
      <string notr="true">QScrollBar:vertical {
    border: none;
    background: white;
    width: 15px;
}

QScrollBar::handle:vertical {
    background: #999999;
    min-height: 20px;
    border-radius: 10px;
}

QScrollBar::handle:vertical:hover,
QScrollBar::handle:vertical:focus {
    background-color: #666666;
    border-radius: 10px;
}
</string>
     </property>
     <property name="frameShape">
      <enum>QFrame::NoFrame</enum>
     </property>
     <property name="readOnly">
      <bool>true</bool>
     </property>
    </widget>
   </item>
  </layout>
 </widget>
 <customwidgets>
  <customwidget>
   <class>SendTextEdit</class>
   <extends>QTextEdit</extends>
   <header location="global">sendtextedit.h</header>
  </customwidget>
 </customwidgets>
 <resources/>
 <connections/>
</ui>
