
# Consider dependencies only in project.
set(CMAKE_DEPENDS_IN_PROJECT_ONLY OFF)

# The set of languages for which implicit dependencies are needed:
set(CMAKE_DEPENDS_LANGUAGES
  )

# The set of dependency files which are needed:
set(CMAKE_DEPENDS_DEPENDENCY_FILES
  "/home/<USER>/OurChat/server/thirdparty/SQLiteCpp/src/Backup.cpp" "thirdparty/SQLiteCpp/CMakeFiles/SQLiteCpp.dir/src/Backup.cpp.o" "gcc" "thirdparty/SQLiteCpp/CMakeFiles/SQLiteCpp.dir/src/Backup.cpp.o.d"
  "/home/<USER>/OurChat/server/thirdparty/SQLiteCpp/src/Column.cpp" "thirdparty/SQLiteCpp/CMakeFiles/SQLiteCpp.dir/src/Column.cpp.o" "gcc" "thirdparty/SQLiteCpp/CMakeFiles/SQLiteCpp.dir/src/Column.cpp.o.d"
  "/home/<USER>/OurChat/server/thirdparty/SQLiteCpp/src/Database.cpp" "thirdparty/SQLiteCpp/CMakeFiles/SQLiteCpp.dir/src/Database.cpp.o" "gcc" "thirdparty/SQLiteCpp/CMakeFiles/SQLiteCpp.dir/src/Database.cpp.o.d"
  "/home/<USER>/OurChat/server/thirdparty/SQLiteCpp/src/Exception.cpp" "thirdparty/SQLiteCpp/CMakeFiles/SQLiteCpp.dir/src/Exception.cpp.o" "gcc" "thirdparty/SQLiteCpp/CMakeFiles/SQLiteCpp.dir/src/Exception.cpp.o.d"
  "/home/<USER>/OurChat/server/thirdparty/SQLiteCpp/src/Savepoint.cpp" "thirdparty/SQLiteCpp/CMakeFiles/SQLiteCpp.dir/src/Savepoint.cpp.o" "gcc" "thirdparty/SQLiteCpp/CMakeFiles/SQLiteCpp.dir/src/Savepoint.cpp.o.d"
  "/home/<USER>/OurChat/server/thirdparty/SQLiteCpp/src/Statement.cpp" "thirdparty/SQLiteCpp/CMakeFiles/SQLiteCpp.dir/src/Statement.cpp.o" "gcc" "thirdparty/SQLiteCpp/CMakeFiles/SQLiteCpp.dir/src/Statement.cpp.o.d"
  "/home/<USER>/OurChat/server/thirdparty/SQLiteCpp/src/Transaction.cpp" "thirdparty/SQLiteCpp/CMakeFiles/SQLiteCpp.dir/src/Transaction.cpp.o" "gcc" "thirdparty/SQLiteCpp/CMakeFiles/SQLiteCpp.dir/src/Transaction.cpp.o.d"
  )

# Targets to which this target links.
set(CMAKE_TARGET_LINKED_INFO_FILES
  "/home/<USER>/OurChat/server/build/thirdparty/SQLiteCpp/sqlite3/CMakeFiles/sqlite3.dir/DependInfo.cmake"
  )

# Fortran module output directory.
set(CMAKE_Fortran_TARGET_MODULE_DIR "")
