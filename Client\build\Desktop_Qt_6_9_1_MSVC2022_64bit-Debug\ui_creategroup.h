/********************************************************************************
** Form generated from reading UI file 'creategroup.ui'
**
** Created by: Qt User Interface Compiler version 6.9.1
**
** WARNING! All changes made in this file will be lost when recompiling UI file!
********************************************************************************/

#ifndef UI_CREATEGROUP_H
#define UI_CREATEGROUP_H

#include <QtCore/QVariant>
#include <QtWidgets/QApplication>
#include <QtWidgets/QDialog>
#include <QtWidgets/QLabel>
#include <QtWidgets/QLineEdit>
#include <QtWidgets/QPushButton>
#include <QtWidgets/QTextEdit>

QT_BEGIN_NAMESPACE

class Ui_CreateGroup
{
public:
    QLabel *label_title;
    QLabel *label_groupName;
    QLineEdit *lineEdit_groupName;
    QLabel *label_description;
    QTextEdit *textEdit_description;
    QPushButton *pushButton_create;
    QPushButton *pushButton_cancel;

    void setupUi(QDialog *CreateGroup)
    {
        if (CreateGroup->objectName().isEmpty())
            CreateGroup->setObjectName("CreateGroup");
        CreateGroup->resize(400, 300);
        CreateGroup->setStyleSheet(QString::fromUtf8("QDialog {\n"
"    background-color: rgb(255, 255, 255);\n"
"    border-radius: 10px;\n"
"}\n"
"\n"
"QLabel {\n"
"    color: rgb(51, 51, 51);\n"
"    font-size: 14px;\n"
"    font-weight: bold;\n"
"}\n"
"\n"
"QLineEdit, QTextEdit {\n"
"    border: 2px solid rgb(200, 200, 200);\n"
"    border-radius: 5px;\n"
"    padding: 8px;\n"
"    font-size: 12px;\n"
"    background-color: rgb(250, 250, 250);\n"
"}\n"
"\n"
"QLineEdit:focus, QTextEdit:focus {\n"
"    border-color: rgb(64, 158, 255);\n"
"}\n"
"\n"
"QPushButton {\n"
"    background-color: rgb(64, 158, 255);\n"
"    color: white;\n"
"    border: none;\n"
"    border-radius: 5px;\n"
"    padding: 8px 16px;\n"
"    font-size: 12px;\n"
"    font-weight: bold;\n"
"}\n"
"\n"
"QPushButton:hover {\n"
"    background-color: rgb(100, 180, 255);\n"
"}\n"
"\n"
"QPushButton:pressed {\n"
"    background-color: rgb(40, 140, 230);\n"
"}\n"
"\n"
"QPushButton#pushButton_cancel {\n"
"    background-color: rgb(150, 150, 150);\n"
"}\n"
"\n"
"QPushButton#pushButton_cancel:hover {\n"
""
                        "    background-color: rgb(170, 170, 170);\n"
"}"));
        label_title = new QLabel(CreateGroup);
        label_title->setObjectName("label_title");
        label_title->setGeometry(QRect(20, 20, 360, 30));
        label_title->setAlignment(Qt::AlignCenter);
        label_title->setStyleSheet(QString::fromUtf8("font-size: 18px;"));
        label_groupName = new QLabel(CreateGroup);
        label_groupName->setObjectName("label_groupName");
        label_groupName->setGeometry(QRect(30, 70, 80, 25));
        lineEdit_groupName = new QLineEdit(CreateGroup);
        lineEdit_groupName->setObjectName("lineEdit_groupName");
        lineEdit_groupName->setGeometry(QRect(120, 70, 250, 30));
        lineEdit_groupName->setMaxLength(20);
        label_description = new QLabel(CreateGroup);
        label_description->setObjectName("label_description");
        label_description->setGeometry(QRect(30, 120, 80, 25));
        textEdit_description = new QTextEdit(CreateGroup);
        textEdit_description->setObjectName("textEdit_description");
        textEdit_description->setGeometry(QRect(120, 120, 250, 80));
        pushButton_create = new QPushButton(CreateGroup);
        pushButton_create->setObjectName("pushButton_create");
        pushButton_create->setGeometry(QRect(120, 230, 100, 35));
        pushButton_cancel = new QPushButton(CreateGroup);
        pushButton_cancel->setObjectName("pushButton_cancel");
        pushButton_cancel->setGeometry(QRect(250, 230, 100, 35));

        retranslateUi(CreateGroup);

        QMetaObject::connectSlotsByName(CreateGroup);
    } // setupUi

    void retranslateUi(QDialog *CreateGroup)
    {
        CreateGroup->setWindowTitle(QCoreApplication::translate("CreateGroup", "\345\210\233\345\273\272\347\276\244\350\201\212", nullptr));
        label_title->setText(QCoreApplication::translate("CreateGroup", "\345\210\233\345\273\272\347\276\244\350\201\212", nullptr));
        label_groupName->setText(QCoreApplication::translate("CreateGroup", "\347\276\244\345\220\215\347\247\260\357\274\232", nullptr));
        label_description->setText(QCoreApplication::translate("CreateGroup", "\347\276\244\346\217\217\350\277\260\357\274\232", nullptr));
        pushButton_create->setText(QCoreApplication::translate("CreateGroup", "\345\210\233\345\273\272\347\276\244\350\201\212", nullptr));
        pushButton_cancel->setText(QCoreApplication::translate("CreateGroup", "\345\217\226\346\266\210", nullptr));
    } // retranslateUi

};

namespace Ui {
    class CreateGroup: public Ui_CreateGroup {};
} // namespace Ui

QT_END_NAMESPACE

#endif // UI_CREATEGROUP_H
