thirdparty/SQLiteCpp/CMakeFiles/SQLiteCpp.dir/src/Backup.cpp.o: \
 /home/<USER>/OurChat/server/thirdparty/SQLiteCpp/src/Backup.cpp \
 /usr/include/stdc-predef.h \
 /home/<USER>/OurChat/server/thirdparty/SQLiteCpp/include/SQLiteCpp/Backup.h \
 /home/<USER>/OurChat/server/thirdparty/SQLiteCpp/include/SQLiteCpp/SQLiteCppExport.h \
 /home/<USER>/OurChat/server/thirdparty/SQLiteCpp/include/SQLiteCpp/Database.h \
 /home/<USER>/OurChat/server/thirdparty/SQLiteCpp/include/SQLiteCpp/Column.h \
 /home/<USER>/OurChat/server/thirdparty/SQLiteCpp/include/SQLiteCpp/Statement.h \
 /home/<USER>/OurChat/server/thirdparty/SQLiteCpp/include/SQLiteCpp/Exception.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/stdexcept \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/exception \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/x86_64-redhat-linux/bits/c++config.h \
 /usr/include/bits/wordsize.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/x86_64-redhat-linux/bits/os_defines.h \
 /usr/include/features.h /usr/include/sys/cdefs.h \
 /usr/include/gnu/stubs.h /usr/include/gnu/stubs-64.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/x86_64-redhat-linux/bits/cpu_defines.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/bits/exception.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/bits/exception_ptr.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/bits/exception_defines.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/bits/cxxabi_init_exception.h \
 /opt/rh/devtoolset-8/root/usr/lib/gcc/x86_64-redhat-linux/8/include/stddef.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/typeinfo \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/bits/hash_bytes.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/new \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/bits/nested_exception.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/bits/move.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/bits/concept_check.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/type_traits \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/string \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/bits/stringfwd.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/bits/memoryfwd.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/bits/char_traits.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/bits/stl_algobase.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/bits/functexcept.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/bits/cpp_type_traits.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/ext/type_traits.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/ext/numeric_traits.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/bits/stl_pair.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/bits/stl_iterator_base_types.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/bits/stl_iterator_base_funcs.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/debug/assertions.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/bits/stl_iterator.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/bits/ptr_traits.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/debug/debug.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/bits/predefined_ops.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/bits/postypes.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/cwchar /usr/include/wchar.h \
 /usr/include/stdio.h \
 /opt/rh/devtoolset-8/root/usr/lib/gcc/x86_64-redhat-linux/8/include/stdarg.h \
 /usr/include/bits/wchar.h /usr/include/xlocale.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/cstdint \
 /opt/rh/devtoolset-8/root/usr/lib/gcc/x86_64-redhat-linux/8/include/stdint.h \
 /usr/include/stdint.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/bits/allocator.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/x86_64-redhat-linux/bits/c++allocator.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/ext/new_allocator.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/bits/localefwd.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/x86_64-redhat-linux/bits/c++locale.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/clocale \
 /usr/include/locale.h /usr/include/bits/locale.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/iosfwd \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/cctype /usr/include/ctype.h \
 /usr/include/bits/types.h /usr/include/bits/typesizes.h \
 /usr/include/endian.h /usr/include/bits/endian.h \
 /usr/include/bits/byteswap.h /usr/include/bits/byteswap-16.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/bits/ostream_insert.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/bits/cxxabi_forced.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/bits/stl_function.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/backward/binders.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/bits/range_access.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/initializer_list \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/bits/basic_string.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/ext/atomicity.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/x86_64-redhat-linux/bits/gthr.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/x86_64-redhat-linux/bits/gthr-default.h \
 /usr/include/pthread.h /usr/include/sched.h /usr/include/time.h \
 /usr/include/bits/sched.h /usr/include/bits/time.h \
 /usr/include/bits/timex.h /usr/include/bits/pthreadtypes.h \
 /usr/include/bits/setjmp.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/x86_64-redhat-linux/bits/atomic_word.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/ext/alloc_traits.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/bits/alloc_traits.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/string_view \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/limits \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/bits/functional_hash.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/bits/string_view.tcc \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/ext/string_conversions.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/cstdlib \
 /usr/include/stdlib.h /usr/include/bits/waitflags.h \
 /usr/include/bits/waitstatus.h /usr/include/sys/types.h \
 /usr/include/sys/select.h /usr/include/bits/select.h \
 /usr/include/bits/sigset.h /usr/include/sys/sysmacros.h \
 /usr/include/alloca.h /usr/include/bits/stdlib-float.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/bits/std_abs.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/cstdio /usr/include/libio.h \
 /usr/include/_G_config.h /usr/include/bits/stdio_lim.h \
 /usr/include/bits/sys_errlist.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/cerrno /usr/include/errno.h \
 /usr/include/bits/errno.h /usr/include/linux/errno.h \
 /usr/include/asm/errno.h /usr/include/asm-generic/errno.h \
 /usr/include/asm-generic/errno-base.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/bits/basic_string.tcc \
 /home/<USER>/OurChat/server/thirdparty/SQLiteCpp/include/SQLiteCpp/Utils.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/map \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/bits/stl_tree.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/ext/aligned_buffer.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/bits/node_handle.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/optional \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/utility \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/bits/stl_relops.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/bits/enable_special_members.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/bits/stl_map.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/tuple \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/array \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/bits/uses_allocator.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/bits/invoke.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/bits/stl_multimap.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/memory \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/bits/stl_construct.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/bits/stl_uninitialized.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/bits/stl_tempbuf.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/bits/stl_raw_storage_iter.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/ext/concurrence.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/bits/unique_ptr.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/bits/shared_ptr.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/bits/shared_ptr_base.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/bits/allocated_ptr.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/bits/refwrap.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/bits/shared_ptr_atomic.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/bits/atomic_base.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/bits/atomic_lockfree_defines.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/backward/auto_ptr.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/filesystem \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/bits/fs_fwd.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/system_error \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/x86_64-redhat-linux/bits/error_constants.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/chrono \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/ratio \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/ctime \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/bits/parse_numbers.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/bits/fs_path.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/vector \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/bits/stl_vector.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/bits/stl_bvector.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/bits/vector.tcc \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/locale \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/bits/locale_classes.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/bits/locale_classes.tcc \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/bits/locale_facets.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/cwctype \
 /usr/include/wctype.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/x86_64-redhat-linux/bits/ctype_base.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/bits/ios_base.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/streambuf \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/bits/streambuf.tcc \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/bits/streambuf_iterator.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/x86_64-redhat-linux/bits/ctype_inline.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/bits/locale_facets.tcc \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/bits/locale_facets_nonio.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/x86_64-redhat-linux/bits/time_members.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/x86_64-redhat-linux/bits/messages_members.h \
 /usr/include/libintl.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/bits/codecvt.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/bits/locale_facets_nonio.tcc \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/bits/locale_conv.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/codecvt \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/bits/quoted_string.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/sstream \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/istream \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/ios \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/bits/basic_ios.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/bits/basic_ios.tcc \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/ostream \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/bits/ostream.tcc \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/bits/istream.tcc \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/bits/sstream.tcc \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/bits/fs_dir.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/bits/fs_ops.h \
 /usr/include/string.h \
 /home/<USER>/OurChat/server/thirdparty/SQLiteCpp/sqlite3/sqlite3.h
