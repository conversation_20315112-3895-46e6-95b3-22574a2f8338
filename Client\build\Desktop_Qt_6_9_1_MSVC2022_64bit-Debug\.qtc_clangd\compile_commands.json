[{"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-nologo", "-Zc:wchar_t", "-FS", "-Zc:rvalueCast", "-Zc:inline", "-Zc:strictStrings", "-Zc:<PERSON><PERSON><PERSON>", "-permissive-", "-Zc:__cplusplus", "-Zc:externConstexpr", "-<PERSON>i", "-MDd", "-clang:-std=c++17", "-utf-8", "-W3", "-w34100", "-w34189", "-w44456", "-w44457", "-w44458", "-wd4577", "-wd4467", "-EHsc", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "-fms-compatibility-version=19.44", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-DWIN64", "-DRC_ICONS", "-DQT_DEPRECATED_WARNINGS", "-DQT_QML_DEBUG", "-DQT_OPENGLWIDGETS_LIB", "-DQT_OPENGL_LIB", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IE:\\YingYong\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IE:\\YingYong\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\Desktop\\100\\chat-forge-master\\Client", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\Adding", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\Chatting", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\IconSetting", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\Tools", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\Rewriting", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtOpenGLWidgets", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtOpenGL", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtWidgets", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtGui", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtNetwork", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtCore", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\build\\Desktop_Qt_6_9_1_MSVC2022_64bit-Debug\\debug", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\build\\Desktop_Qt_6_9_1_MSVC2022_64bit-Debug", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:E:\\YingYong\\QT\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Tools\\MSVC\\14.44.35207\\ATLMFC\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\um", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\shared", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\winrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\cppwinrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\NETFXSDK\\4.8\\include\\um", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/TP", "D:\\Desktop\\100\\chat-forge-master\\Client\\Adding\\addfriend.cpp"], "directory": "D:/Desktop/100/chat-forge-master/Client/build/Desktop_Qt_6_9_1_MSVC2022_64bit-Debug/.qtc_clangd", "file": "D:/Desktop/100/chat-forge-master/Client/Adding/addfriend.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-nologo", "-Zc:wchar_t", "-FS", "-Zc:rvalueCast", "-Zc:inline", "-Zc:strictStrings", "-Zc:<PERSON><PERSON><PERSON>", "-permissive-", "-Zc:__cplusplus", "-Zc:externConstexpr", "-<PERSON>i", "-MDd", "-clang:-std=c++17", "-utf-8", "-W3", "-w34100", "-w34189", "-w44456", "-w44457", "-w44458", "-wd4577", "-wd4467", "-EHsc", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "-fms-compatibility-version=19.44", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-DWIN64", "-DRC_ICONS", "-DQT_DEPRECATED_WARNINGS", "-DQT_QML_DEBUG", "-DQT_OPENGLWIDGETS_LIB", "-DQT_OPENGL_LIB", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IE:\\YingYong\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IE:\\YingYong\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\Desktop\\100\\chat-forge-master\\Client", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\Adding", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\Chatting", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\IconSetting", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\Tools", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\Rewriting", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtOpenGLWidgets", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtOpenGL", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtWidgets", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtGui", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtNetwork", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtCore", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\build\\Desktop_Qt_6_9_1_MSVC2022_64bit-Debug\\debug", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\build\\Desktop_Qt_6_9_1_MSVC2022_64bit-Debug", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:E:\\YingYong\\QT\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Tools\\MSVC\\14.44.35207\\ATLMFC\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\um", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\shared", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\winrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\cppwinrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\NETFXSDK\\4.8\\include\\um", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/TP", "D:\\Desktop\\100\\chat-forge-master\\Client\\Adding\\creategroup.cpp"], "directory": "D:/Desktop/100/chat-forge-master/Client/build/Desktop_Qt_6_9_1_MSVC2022_64bit-Debug/.qtc_clangd", "file": "D:/Desktop/100/chat-forge-master/Client/Adding/creategroup.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-nologo", "-Zc:wchar_t", "-FS", "-Zc:rvalueCast", "-Zc:inline", "-Zc:strictStrings", "-Zc:<PERSON><PERSON><PERSON>", "-permissive-", "-Zc:__cplusplus", "-Zc:externConstexpr", "-<PERSON>i", "-MDd", "-clang:-std=c++17", "-utf-8", "-W3", "-w34100", "-w34189", "-w44456", "-w44457", "-w44458", "-wd4577", "-wd4467", "-EHsc", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "-fms-compatibility-version=19.44", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-DWIN64", "-DRC_ICONS", "-DQT_DEPRECATED_WARNINGS", "-DQT_QML_DEBUG", "-DQT_OPENGLWIDGETS_LIB", "-DQT_OPENGL_LIB", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IE:\\YingYong\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IE:\\YingYong\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\Desktop\\100\\chat-forge-master\\Client", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\Adding", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\Chatting", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\IconSetting", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\Tools", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\Rewriting", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtOpenGLWidgets", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtOpenGL", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtWidgets", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtGui", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtNetwork", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtCore", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\build\\Desktop_Qt_6_9_1_MSVC2022_64bit-Debug\\debug", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\build\\Desktop_Qt_6_9_1_MSVC2022_64bit-Debug", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:E:\\YingYong\\QT\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Tools\\MSVC\\14.44.35207\\ATLMFC\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\um", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\shared", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\winrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\cppwinrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\NETFXSDK\\4.8\\include\\um", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/TP", "D:\\Desktop\\100\\chat-forge-master\\Client\\Adding\\systemmessage.cpp"], "directory": "D:/Desktop/100/chat-forge-master/Client/build/Desktop_Qt_6_9_1_MSVC2022_64bit-Debug/.qtc_clangd", "file": "D:/Desktop/100/chat-forge-master/Client/Adding/systemmessage.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-nologo", "-Zc:wchar_t", "-FS", "-Zc:rvalueCast", "-Zc:inline", "-Zc:strictStrings", "-Zc:<PERSON><PERSON><PERSON>", "-permissive-", "-Zc:__cplusplus", "-Zc:externConstexpr", "-<PERSON>i", "-MDd", "-clang:-std=c++17", "-utf-8", "-W3", "-w34100", "-w34189", "-w44456", "-w44457", "-w44458", "-wd4577", "-wd4467", "-EHsc", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "-fms-compatibility-version=19.44", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-DWIN64", "-DRC_ICONS", "-DQT_DEPRECATED_WARNINGS", "-DQT_QML_DEBUG", "-DQT_OPENGLWIDGETS_LIB", "-DQT_OPENGL_LIB", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IE:\\YingYong\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IE:\\YingYong\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\Desktop\\100\\chat-forge-master\\Client", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\Adding", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\Chatting", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\IconSetting", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\Tools", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\Rewriting", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtOpenGLWidgets", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtOpenGL", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtWidgets", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtGui", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtNetwork", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtCore", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\build\\Desktop_Qt_6_9_1_MSVC2022_64bit-Debug\\debug", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\build\\Desktop_Qt_6_9_1_MSVC2022_64bit-Debug", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:E:\\YingYong\\QT\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Tools\\MSVC\\14.44.35207\\ATLMFC\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\um", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\shared", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\winrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\cppwinrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\NETFXSDK\\4.8\\include\\um", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/TP", "D:\\Desktop\\100\\chat-forge-master\\Client\\Adding\\verificationitem.cpp"], "directory": "D:/Desktop/100/chat-forge-master/Client/build/Desktop_Qt_6_9_1_MSVC2022_64bit-Debug/.qtc_clangd", "file": "D:/Desktop/100/chat-forge-master/Client/Adding/verificationitem.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-nologo", "-Zc:wchar_t", "-FS", "-Zc:rvalueCast", "-Zc:inline", "-Zc:strictStrings", "-Zc:<PERSON><PERSON><PERSON>", "-permissive-", "-Zc:__cplusplus", "-Zc:externConstexpr", "-<PERSON>i", "-MDd", "-clang:-std=c++17", "-utf-8", "-W3", "-w34100", "-w34189", "-w44456", "-w44457", "-w44458", "-wd4577", "-wd4467", "-EHsc", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "-fms-compatibility-version=19.44", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-DWIN64", "-DRC_ICONS", "-DQT_DEPRECATED_WARNINGS", "-DQT_QML_DEBUG", "-DQT_OPENGLWIDGETS_LIB", "-DQT_OPENGL_LIB", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IE:\\YingYong\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IE:\\YingYong\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\Desktop\\100\\chat-forge-master\\Client", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\Adding", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\Chatting", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\IconSetting", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\Tools", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\Rewriting", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtOpenGLWidgets", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtOpenGL", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtWidgets", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtGui", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtNetwork", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtCore", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\build\\Desktop_Qt_6_9_1_MSVC2022_64bit-Debug\\debug", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\build\\Desktop_Qt_6_9_1_MSVC2022_64bit-Debug", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:E:\\YingYong\\QT\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Tools\\MSVC\\14.44.35207\\ATLMFC\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\um", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\shared", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\winrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\cppwinrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\NETFXSDK\\4.8\\include\\um", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/TP", "D:\\Desktop\\100\\chat-forge-master\\Client\\Chatting\\chatlistwidget.cpp"], "directory": "D:/Desktop/100/chat-forge-master/Client/build/Desktop_Qt_6_9_1_MSVC2022_64bit-Debug/.qtc_clangd", "file": "D:/Desktop/100/chat-forge-master/Client/Chatting/chatlistwidget.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-nologo", "-Zc:wchar_t", "-FS", "-Zc:rvalueCast", "-Zc:inline", "-Zc:strictStrings", "-Zc:<PERSON><PERSON><PERSON>", "-permissive-", "-Zc:__cplusplus", "-Zc:externConstexpr", "-<PERSON>i", "-MDd", "-clang:-std=c++17", "-utf-8", "-W3", "-w34100", "-w34189", "-w44456", "-w44457", "-w44458", "-wd4577", "-wd4467", "-EHsc", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "-fms-compatibility-version=19.44", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-DWIN64", "-DRC_ICONS", "-DQT_DEPRECATED_WARNINGS", "-DQT_QML_DEBUG", "-DQT_OPENGLWIDGETS_LIB", "-DQT_OPENGL_LIB", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IE:\\YingYong\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IE:\\YingYong\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\Desktop\\100\\chat-forge-master\\Client", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\Adding", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\Chatting", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\IconSetting", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\Tools", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\Rewriting", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtOpenGLWidgets", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtOpenGL", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtWidgets", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtGui", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtNetwork", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtCore", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\build\\Desktop_Qt_6_9_1_MSVC2022_64bit-Debug\\debug", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\build\\Desktop_Qt_6_9_1_MSVC2022_64bit-Debug", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:E:\\YingYong\\QT\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Tools\\MSVC\\14.44.35207\\ATLMFC\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\um", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\shared", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\winrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\cppwinrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\NETFXSDK\\4.8\\include\\um", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/TP", "D:\\Desktop\\100\\chat-forge-master\\Client\\Chatting\\chatwindow.cpp"], "directory": "D:/Desktop/100/chat-forge-master/Client/build/Desktop_Qt_6_9_1_MSVC2022_64bit-Debug/.qtc_clangd", "file": "D:/Desktop/100/chat-forge-master/Client/Chatting/chatwindow.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-nologo", "-Zc:wchar_t", "-FS", "-Zc:rvalueCast", "-Zc:inline", "-Zc:strictStrings", "-Zc:<PERSON><PERSON><PERSON>", "-permissive-", "-Zc:__cplusplus", "-Zc:externConstexpr", "-<PERSON>i", "-MDd", "-clang:-std=c++17", "-utf-8", "-W3", "-w34100", "-w34189", "-w44456", "-w44457", "-w44458", "-wd4577", "-wd4467", "-EHsc", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "-fms-compatibility-version=19.44", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-DWIN64", "-DRC_ICONS", "-DQT_DEPRECATED_WARNINGS", "-DQT_QML_DEBUG", "-DQT_OPENGLWIDGETS_LIB", "-DQT_OPENGL_LIB", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IE:\\YingYong\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IE:\\YingYong\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\Desktop\\100\\chat-forge-master\\Client", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\Adding", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\Chatting", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\IconSetting", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\Tools", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\Rewriting", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtOpenGLWidgets", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtOpenGL", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtWidgets", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtGui", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtNetwork", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtCore", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\build\\Desktop_Qt_6_9_1_MSVC2022_64bit-Debug\\debug", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\build\\Desktop_Qt_6_9_1_MSVC2022_64bit-Debug", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:E:\\YingYong\\QT\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Tools\\MSVC\\14.44.35207\\ATLMFC\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\um", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\shared", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\winrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\cppwinrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\NETFXSDK\\4.8\\include\\um", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/TP", "D:\\Desktop\\100\\chat-forge-master\\Client\\Chatting\\emojiselector.cpp"], "directory": "D:/Desktop/100/chat-forge-master/Client/build/Desktop_Qt_6_9_1_MSVC2022_64bit-Debug/.qtc_clangd", "file": "D:/Desktop/100/chat-forge-master/Client/Chatting/emojiselector.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-nologo", "-Zc:wchar_t", "-FS", "-Zc:rvalueCast", "-Zc:inline", "-Zc:strictStrings", "-Zc:<PERSON><PERSON><PERSON>", "-permissive-", "-Zc:__cplusplus", "-Zc:externConstexpr", "-<PERSON>i", "-MDd", "-clang:-std=c++17", "-utf-8", "-W3", "-w34100", "-w34189", "-w44456", "-w44457", "-w44458", "-wd4577", "-wd4467", "-EHsc", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "-fms-compatibility-version=19.44", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-DWIN64", "-DRC_ICONS", "-DQT_DEPRECATED_WARNINGS", "-DQT_QML_DEBUG", "-DQT_OPENGLWIDGETS_LIB", "-DQT_OPENGL_LIB", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IE:\\YingYong\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IE:\\YingYong\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\Desktop\\100\\chat-forge-master\\Client", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\Adding", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\Chatting", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\IconSetting", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\Tools", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\Rewriting", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtOpenGLWidgets", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtOpenGL", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtWidgets", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtGui", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtNetwork", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtCore", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\build\\Desktop_Qt_6_9_1_MSVC2022_64bit-Debug\\debug", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\build\\Desktop_Qt_6_9_1_MSVC2022_64bit-Debug", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:E:\\YingYong\\QT\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Tools\\MSVC\\14.44.35207\\ATLMFC\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\um", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\shared", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\winrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\cppwinrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\NETFXSDK\\4.8\\include\\um", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/TP", "D:\\Desktop\\100\\chat-forge-master\\Client\\IconSetting\\iconselect.cpp"], "directory": "D:/Desktop/100/chat-forge-master/Client/build/Desktop_Qt_6_9_1_MSVC2022_64bit-Debug/.qtc_clangd", "file": "D:/Desktop/100/chat-forge-master/Client/IconSetting/iconselect.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-nologo", "-Zc:wchar_t", "-FS", "-Zc:rvalueCast", "-Zc:inline", "-Zc:strictStrings", "-Zc:<PERSON><PERSON><PERSON>", "-permissive-", "-Zc:__cplusplus", "-Zc:externConstexpr", "-<PERSON>i", "-MDd", "-clang:-std=c++17", "-utf-8", "-W3", "-w34100", "-w34189", "-w44456", "-w44457", "-w44458", "-wd4577", "-wd4467", "-EHsc", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "-fms-compatibility-version=19.44", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-DWIN64", "-DRC_ICONS", "-DQT_DEPRECATED_WARNINGS", "-DQT_QML_DEBUG", "-DQT_OPENGLWIDGETS_LIB", "-DQT_OPENGL_LIB", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IE:\\YingYong\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IE:\\YingYong\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\Desktop\\100\\chat-forge-master\\Client", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\Adding", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\Chatting", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\IconSetting", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\Tools", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\Rewriting", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtOpenGLWidgets", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtOpenGL", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtWidgets", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtGui", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtNetwork", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtCore", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\build\\Desktop_Qt_6_9_1_MSVC2022_64bit-Debug\\debug", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\build\\Desktop_Qt_6_9_1_MSVC2022_64bit-Debug", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:E:\\YingYong\\QT\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Tools\\MSVC\\14.44.35207\\ATLMFC\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\um", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\shared", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\winrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\cppwinrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\NETFXSDK\\4.8\\include\\um", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/TP", "D:\\Desktop\\100\\chat-forge-master\\Client\\Rewriting\\friendiconlabel.cpp"], "directory": "D:/Desktop/100/chat-forge-master/Client/build/Desktop_Qt_6_9_1_MSVC2022_64bit-Debug/.qtc_clangd", "file": "D:/Desktop/100/chat-forge-master/Client/Rewriting/friendiconlabel.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-nologo", "-Zc:wchar_t", "-FS", "-Zc:rvalueCast", "-Zc:inline", "-Zc:strictStrings", "-Zc:<PERSON><PERSON><PERSON>", "-permissive-", "-Zc:__cplusplus", "-Zc:externConstexpr", "-<PERSON>i", "-MDd", "-clang:-std=c++17", "-utf-8", "-W3", "-w34100", "-w34189", "-w44456", "-w44457", "-w44458", "-wd4577", "-wd4467", "-EHsc", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "-fms-compatibility-version=19.44", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-DWIN64", "-DRC_ICONS", "-DQT_DEPRECATED_WARNINGS", "-DQT_QML_DEBUG", "-DQT_OPENGLWIDGETS_LIB", "-DQT_OPENGL_LIB", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IE:\\YingYong\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IE:\\YingYong\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\Desktop\\100\\chat-forge-master\\Client", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\Adding", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\Chatting", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\IconSetting", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\Tools", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\Rewriting", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtOpenGLWidgets", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtOpenGL", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtWidgets", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtGui", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtNetwork", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtCore", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\build\\Desktop_Qt_6_9_1_MSVC2022_64bit-Debug\\debug", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\build\\Desktop_Qt_6_9_1_MSVC2022_64bit-Debug", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:E:\\YingYong\\QT\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Tools\\MSVC\\14.44.35207\\ATLMFC\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\um", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\shared", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\winrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\cppwinrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\NETFXSDK\\4.8\\include\\um", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/TP", "D:\\Desktop\\100\\chat-forge-master\\Client\\Rewriting\\smoothscrolllistwidget.cpp"], "directory": "D:/Desktop/100/chat-forge-master/Client/build/Desktop_Qt_6_9_1_MSVC2022_64bit-Debug/.qtc_clangd", "file": "D:/Desktop/100/chat-forge-master/Client/Rewriting/smoothscrolllistwidget.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-nologo", "-Zc:wchar_t", "-FS", "-Zc:rvalueCast", "-Zc:inline", "-Zc:strictStrings", "-Zc:<PERSON><PERSON><PERSON>", "-permissive-", "-Zc:__cplusplus", "-Zc:externConstexpr", "-<PERSON>i", "-MDd", "-clang:-std=c++17", "-utf-8", "-W3", "-w34100", "-w34189", "-w44456", "-w44457", "-w44458", "-wd4577", "-wd4467", "-EHsc", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "-fms-compatibility-version=19.44", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-DWIN64", "-DRC_ICONS", "-DQT_DEPRECATED_WARNINGS", "-DQT_QML_DEBUG", "-DQT_OPENGLWIDGETS_LIB", "-DQT_OPENGL_LIB", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IE:\\YingYong\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IE:\\YingYong\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\Desktop\\100\\chat-forge-master\\Client", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\Adding", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\Chatting", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\IconSetting", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\Tools", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\Rewriting", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtOpenGLWidgets", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtOpenGL", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtWidgets", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtGui", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtNetwork", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtCore", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\build\\Desktop_Qt_6_9_1_MSVC2022_64bit-Debug\\debug", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\build\\Desktop_Qt_6_9_1_MSVC2022_64bit-Debug", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:E:\\YingYong\\QT\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Tools\\MSVC\\14.44.35207\\ATLMFC\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\um", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\shared", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\winrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\cppwinrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\NETFXSDK\\4.8\\include\\um", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/TP", "D:\\Desktop\\100\\chat-forge-master\\Client\\Tools\\imagepreview.cpp"], "directory": "D:/Desktop/100/chat-forge-master/Client/build/Desktop_Qt_6_9_1_MSVC2022_64bit-Debug/.qtc_clangd", "file": "D:/Desktop/100/chat-forge-master/Client/Tools/imagepreview.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-nologo", "-Zc:wchar_t", "-FS", "-Zc:rvalueCast", "-Zc:inline", "-Zc:strictStrings", "-Zc:<PERSON><PERSON><PERSON>", "-permissive-", "-Zc:__cplusplus", "-Zc:externConstexpr", "-<PERSON>i", "-MDd", "-clang:-std=c++17", "-utf-8", "-W3", "-w34100", "-w34189", "-w44456", "-w44457", "-w44458", "-wd4577", "-wd4467", "-EHsc", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "-fms-compatibility-version=19.44", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-DWIN64", "-DRC_ICONS", "-DQT_DEPRECATED_WARNINGS", "-DQT_QML_DEBUG", "-DQT_OPENGLWIDGETS_LIB", "-DQT_OPENGL_LIB", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IE:\\YingYong\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IE:\\YingYong\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\Desktop\\100\\chat-forge-master\\Client", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\Adding", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\Chatting", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\IconSetting", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\Tools", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\Rewriting", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtOpenGLWidgets", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtOpenGL", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtWidgets", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtGui", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtNetwork", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtCore", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\build\\Desktop_Qt_6_9_1_MSVC2022_64bit-Debug\\debug", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\build\\Desktop_Qt_6_9_1_MSVC2022_64bit-Debug", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:E:\\YingYong\\QT\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Tools\\MSVC\\14.44.35207\\ATLMFC\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\um", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\shared", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\winrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\cppwinrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\NETFXSDK\\4.8\\include\\um", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/TP", "D:\\Desktop\\100\\chat-forge-master\\Client\\friendinfowidget.cpp"], "directory": "D:/Desktop/100/chat-forge-master/Client/build/Desktop_Qt_6_9_1_MSVC2022_64bit-Debug/.qtc_clangd", "file": "D:/Desktop/100/chat-forge-master/Client/friendinfowidget.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-nologo", "-Zc:wchar_t", "-FS", "-Zc:rvalueCast", "-Zc:inline", "-Zc:strictStrings", "-Zc:<PERSON><PERSON><PERSON>", "-permissive-", "-Zc:__cplusplus", "-Zc:externConstexpr", "-<PERSON>i", "-MDd", "-clang:-std=c++17", "-utf-8", "-W3", "-w34100", "-w34189", "-w44456", "-w44457", "-w44458", "-wd4577", "-wd4467", "-EHsc", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "-fms-compatibility-version=19.44", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-DWIN64", "-DRC_ICONS", "-DQT_DEPRECATED_WARNINGS", "-DQT_QML_DEBUG", "-DQT_OPENGLWIDGETS_LIB", "-DQT_OPENGL_LIB", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IE:\\YingYong\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IE:\\YingYong\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\Desktop\\100\\chat-forge-master\\Client", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\Adding", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\Chatting", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\IconSetting", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\Tools", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\Rewriting", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtOpenGLWidgets", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtOpenGL", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtWidgets", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtGui", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtNetwork", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtCore", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\build\\Desktop_Qt_6_9_1_MSVC2022_64bit-Debug\\debug", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\build\\Desktop_Qt_6_9_1_MSVC2022_64bit-Debug", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:E:\\YingYong\\QT\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Tools\\MSVC\\14.44.35207\\ATLMFC\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\um", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\shared", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\winrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\cppwinrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\NETFXSDK\\4.8\\include\\um", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/TP", "D:\\Desktop\\100\\chat-forge-master\\Client\\Rewriting\\frienditem.cpp"], "directory": "D:/Desktop/100/chat-forge-master/Client/build/Desktop_Qt_6_9_1_MSVC2022_64bit-Debug/.qtc_clangd", "file": "D:/Desktop/100/chat-forge-master/Client/Rewriting/frienditem.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-nologo", "-Zc:wchar_t", "-FS", "-Zc:rvalueCast", "-Zc:inline", "-Zc:strictStrings", "-Zc:<PERSON><PERSON><PERSON>", "-permissive-", "-Zc:__cplusplus", "-Zc:externConstexpr", "-<PERSON>i", "-MDd", "-clang:-std=c++17", "-utf-8", "-W3", "-w34100", "-w34189", "-w44456", "-w44457", "-w44458", "-wd4577", "-wd4467", "-EHsc", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "-fms-compatibility-version=19.44", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-DWIN64", "-DRC_ICONS", "-DQT_DEPRECATED_WARNINGS", "-DQT_QML_DEBUG", "-DQT_OPENGLWIDGETS_LIB", "-DQT_OPENGL_LIB", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IE:\\YingYong\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IE:\\YingYong\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\Desktop\\100\\chat-forge-master\\Client", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\Adding", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\Chatting", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\IconSetting", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\Tools", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\Rewriting", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtOpenGLWidgets", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtOpenGL", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtWidgets", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtGui", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtNetwork", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtCore", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\build\\Desktop_Qt_6_9_1_MSVC2022_64bit-Debug\\debug", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\build\\Desktop_Qt_6_9_1_MSVC2022_64bit-Debug", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:E:\\YingYong\\QT\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Tools\\MSVC\\14.44.35207\\ATLMFC\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\um", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\shared", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\winrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\cppwinrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\NETFXSDK\\4.8\\include\\um", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/TP", "D:\\Desktop\\100\\chat-forge-master\\Client\\Rewriting\\sendtextedit.cpp"], "directory": "D:/Desktop/100/chat-forge-master/Client/build/Desktop_Qt_6_9_1_MSVC2022_64bit-Debug/.qtc_clangd", "file": "D:/Desktop/100/chat-forge-master/Client/Rewriting/sendtextedit.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-nologo", "-Zc:wchar_t", "-FS", "-Zc:rvalueCast", "-Zc:inline", "-Zc:strictStrings", "-Zc:<PERSON><PERSON><PERSON>", "-permissive-", "-Zc:__cplusplus", "-Zc:externConstexpr", "-<PERSON>i", "-MDd", "-clang:-std=c++17", "-utf-8", "-W3", "-w34100", "-w34189", "-w44456", "-w44457", "-w44458", "-wd4577", "-wd4467", "-EHsc", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "-fms-compatibility-version=19.44", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-DWIN64", "-DRC_ICONS", "-DQT_DEPRECATED_WARNINGS", "-DQT_QML_DEBUG", "-DQT_OPENGLWIDGETS_LIB", "-DQT_OPENGL_LIB", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IE:\\YingYong\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IE:\\YingYong\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\Desktop\\100\\chat-forge-master\\Client", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\Adding", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\Chatting", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\IconSetting", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\Tools", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\Rewriting", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtOpenGLWidgets", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtOpenGL", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtWidgets", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtGui", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtNetwork", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtCore", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\build\\Desktop_Qt_6_9_1_MSVC2022_64bit-Debug\\debug", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\build\\Desktop_Qt_6_9_1_MSVC2022_64bit-Debug", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:E:\\YingYong\\QT\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Tools\\MSVC\\14.44.35207\\ATLMFC\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\um", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\shared", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\winrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\cppwinrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\NETFXSDK\\4.8\\include\\um", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/TP", "D:\\Desktop\\100\\chat-forge-master\\Client\\Tools\\stringtool.cpp"], "directory": "D:/Desktop/100/chat-forge-master/Client/build/Desktop_Qt_6_9_1_MSVC2022_64bit-Debug/.qtc_clangd", "file": "D:/Desktop/100/chat-forge-master/Client/Tools/stringtool.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-nologo", "-Zc:wchar_t", "-FS", "-Zc:rvalueCast", "-Zc:inline", "-Zc:strictStrings", "-Zc:<PERSON><PERSON><PERSON>", "-permissive-", "-Zc:__cplusplus", "-Zc:externConstexpr", "-<PERSON>i", "-MDd", "-clang:-std=c++17", "-utf-8", "-W3", "-w34100", "-w34189", "-w44456", "-w44457", "-w44458", "-wd4577", "-wd4467", "-EHsc", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "-fms-compatibility-version=19.44", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-DWIN64", "-DRC_ICONS", "-DQT_DEPRECATED_WARNINGS", "-DQT_QML_DEBUG", "-DQT_OPENGLWIDGETS_LIB", "-DQT_OPENGL_LIB", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IE:\\YingYong\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IE:\\YingYong\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\Desktop\\100\\chat-forge-master\\Client", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\Adding", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\Chatting", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\IconSetting", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\Tools", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\Rewriting", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtOpenGLWidgets", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtOpenGL", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtWidgets", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtGui", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtNetwork", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtCore", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\build\\Desktop_Qt_6_9_1_MSVC2022_64bit-Debug\\debug", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\build\\Desktop_Qt_6_9_1_MSVC2022_64bit-Debug", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:E:\\YingYong\\QT\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Tools\\MSVC\\14.44.35207\\ATLMFC\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\um", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\shared", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\winrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\cppwinrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\NETFXSDK\\4.8\\include\\um", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/TP", "D:\\Desktop\\100\\chat-forge-master\\Client\\Tools\\tcpclient.cpp"], "directory": "D:/Desktop/100/chat-forge-master/Client/build/Desktop_Qt_6_9_1_MSVC2022_64bit-Debug/.qtc_clangd", "file": "D:/Desktop/100/chat-forge-master/Client/Tools/tcpclient.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-nologo", "-Zc:wchar_t", "-FS", "-Zc:rvalueCast", "-Zc:inline", "-Zc:strictStrings", "-Zc:<PERSON><PERSON><PERSON>", "-permissive-", "-Zc:__cplusplus", "-Zc:externConstexpr", "-<PERSON>i", "-MDd", "-clang:-std=c++17", "-utf-8", "-W3", "-w34100", "-w34189", "-w44456", "-w44457", "-w44458", "-wd4577", "-wd4467", "-EHsc", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "-fms-compatibility-version=19.44", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-DWIN64", "-DRC_ICONS", "-DQT_DEPRECATED_WARNINGS", "-DQT_QML_DEBUG", "-DQT_OPENGLWIDGETS_LIB", "-DQT_OPENGL_LIB", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IE:\\YingYong\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IE:\\YingYong\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\Desktop\\100\\chat-forge-master\\Client", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\Adding", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\Chatting", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\IconSetting", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\Tools", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\Rewriting", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtOpenGLWidgets", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtOpenGL", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtWidgets", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtGui", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtNetwork", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtCore", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\build\\Desktop_Qt_6_9_1_MSVC2022_64bit-Debug\\debug", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\build\\Desktop_Qt_6_9_1_MSVC2022_64bit-Debug", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:E:\\YingYong\\QT\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Tools\\MSVC\\14.44.35207\\ATLMFC\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\um", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\shared", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\winrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\cppwinrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\NETFXSDK\\4.8\\include\\um", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/TP", "D:\\Desktop\\100\\chat-forge-master\\Client\\client.cpp"], "directory": "D:/Desktop/100/chat-forge-master/Client/build/Desktop_Qt_6_9_1_MSVC2022_64bit-Debug/.qtc_clangd", "file": "D:/Desktop/100/chat-forge-master/Client/client.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-nologo", "-Zc:wchar_t", "-FS", "-Zc:rvalueCast", "-Zc:inline", "-Zc:strictStrings", "-Zc:<PERSON><PERSON><PERSON>", "-permissive-", "-Zc:__cplusplus", "-Zc:externConstexpr", "-<PERSON>i", "-MDd", "-clang:-std=c++17", "-utf-8", "-W3", "-w34100", "-w34189", "-w44456", "-w44457", "-w44458", "-wd4577", "-wd4467", "-EHsc", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "-fms-compatibility-version=19.44", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-DWIN64", "-DRC_ICONS", "-DQT_DEPRECATED_WARNINGS", "-DQT_QML_DEBUG", "-DQT_OPENGLWIDGETS_LIB", "-DQT_OPENGL_LIB", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IE:\\YingYong\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IE:\\YingYong\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\Desktop\\100\\chat-forge-master\\Client", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\Adding", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\Chatting", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\IconSetting", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\Tools", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\Rewriting", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtOpenGLWidgets", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtOpenGL", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtWidgets", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtGui", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtNetwork", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtCore", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\build\\Desktop_Qt_6_9_1_MSVC2022_64bit-Debug\\debug", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\build\\Desktop_Qt_6_9_1_MSVC2022_64bit-Debug", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:E:\\YingYong\\QT\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Tools\\MSVC\\14.44.35207\\ATLMFC\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\um", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\shared", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\winrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\cppwinrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\NETFXSDK\\4.8\\include\\um", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/TP", "D:\\Desktop\\100\\chat-forge-master\\Client\\logging.cpp"], "directory": "D:/Desktop/100/chat-forge-master/Client/build/Desktop_Qt_6_9_1_MSVC2022_64bit-Debug/.qtc_clangd", "file": "D:/Desktop/100/chat-forge-master/Client/logging.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-nologo", "-Zc:wchar_t", "-FS", "-Zc:rvalueCast", "-Zc:inline", "-Zc:strictStrings", "-Zc:<PERSON><PERSON><PERSON>", "-permissive-", "-Zc:__cplusplus", "-Zc:externConstexpr", "-<PERSON>i", "-MDd", "-clang:-std=c++17", "-utf-8", "-W3", "-w34100", "-w34189", "-w44456", "-w44457", "-w44458", "-wd4577", "-wd4467", "-EHsc", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "-fms-compatibility-version=19.44", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-DWIN64", "-DRC_ICONS", "-DQT_DEPRECATED_WARNINGS", "-DQT_QML_DEBUG", "-DQT_OPENGLWIDGETS_LIB", "-DQT_OPENGL_LIB", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IE:\\YingYong\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IE:\\YingYong\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\Desktop\\100\\chat-forge-master\\Client", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\Adding", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\Chatting", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\IconSetting", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\Tools", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\Rewriting", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtOpenGLWidgets", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtOpenGL", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtWidgets", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtGui", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtNetwork", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtCore", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\build\\Desktop_Qt_6_9_1_MSVC2022_64bit-Debug\\debug", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\build\\Desktop_Qt_6_9_1_MSVC2022_64bit-Debug", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:E:\\YingYong\\QT\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Tools\\MSVC\\14.44.35207\\ATLMFC\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\um", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\shared", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\winrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\cppwinrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\NETFXSDK\\4.8\\include\\um", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/TP", "D:\\Desktop\\100\\chat-forge-master\\Client\\main.cpp"], "directory": "D:/Desktop/100/chat-forge-master/Client/build/Desktop_Qt_6_9_1_MSVC2022_64bit-Debug/.qtc_clangd", "file": "D:/Desktop/100/chat-forge-master/Client/main.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-nologo", "-Zc:wchar_t", "-FS", "-Zc:rvalueCast", "-Zc:inline", "-Zc:strictStrings", "-Zc:<PERSON><PERSON><PERSON>", "-permissive-", "-Zc:__cplusplus", "-Zc:externConstexpr", "-<PERSON>i", "-MDd", "-clang:-std=c++17", "-utf-8", "-W3", "-w34100", "-w34189", "-w44456", "-w44457", "-w44458", "-wd4577", "-wd4467", "-EHsc", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "-fms-compatibility-version=19.44", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-DWIN64", "-DRC_ICONS", "-DQT_DEPRECATED_WARNINGS", "-DQT_QML_DEBUG", "-DQT_OPENGLWIDGETS_LIB", "-DQT_OPENGL_LIB", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IE:\\YingYong\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IE:\\YingYong\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\Desktop\\100\\chat-forge-master\\Client", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\Adding", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\Chatting", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\IconSetting", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\Tools", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\Rewriting", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtOpenGLWidgets", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtOpenGL", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtWidgets", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtGui", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtNetwork", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtCore", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\build\\Desktop_Qt_6_9_1_MSVC2022_64bit-Debug\\debug", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\build\\Desktop_Qt_6_9_1_MSVC2022_64bit-Debug", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:E:\\YingYong\\QT\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Tools\\MSVC\\14.44.35207\\ATLMFC\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\um", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\shared", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\winrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\cppwinrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\NETFXSDK\\4.8\\include\\um", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/TP", "D:\\Desktop\\100\\chat-forge-master\\Client\\selfinfowidget.cpp"], "directory": "D:/Desktop/100/chat-forge-master/Client/build/Desktop_Qt_6_9_1_MSVC2022_64bit-Debug/.qtc_clangd", "file": "D:/Desktop/100/chat-forge-master/Client/selfinfowidget.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-nologo", "-Zc:wchar_t", "-FS", "-Zc:rvalueCast", "-Zc:inline", "-Zc:strictStrings", "-Zc:<PERSON><PERSON><PERSON>", "-permissive-", "-Zc:__cplusplus", "-Zc:externConstexpr", "-<PERSON>i", "-MDd", "-clang:-std=c++17", "-utf-8", "-W3", "-w34100", "-w34189", "-w44456", "-w44457", "-w44458", "-wd4577", "-wd4467", "-EHsc", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "-fms-compatibility-version=19.44", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-DWIN64", "-DRC_ICONS", "-DQT_DEPRECATED_WARNINGS", "-DQT_QML_DEBUG", "-DQT_OPENGLWIDGETS_LIB", "-DQT_OPENGL_LIB", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IE:\\YingYong\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IE:\\YingYong\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\Desktop\\100\\chat-forge-master\\Client", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\Adding", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\Chatting", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\IconSetting", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\Tools", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\Rewriting", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtOpenGLWidgets", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtOpenGL", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtWidgets", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtGui", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtNetwork", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtCore", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\build\\Desktop_Qt_6_9_1_MSVC2022_64bit-Debug\\debug", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\build\\Desktop_Qt_6_9_1_MSVC2022_64bit-Debug", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:E:\\YingYong\\QT\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Tools\\MSVC\\14.44.35207\\ATLMFC\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\um", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\shared", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\winrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\cppwinrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\NETFXSDK\\4.8\\include\\um", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/TP", "D:\\Desktop\\100\\chat-forge-master\\Client\\Adding\\addfriend.h"], "directory": "D:/Desktop/100/chat-forge-master/Client/build/Desktop_Qt_6_9_1_MSVC2022_64bit-Debug/.qtc_clangd", "file": "D:/Desktop/100/chat-forge-master/Client/Adding/addfriend.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-nologo", "-Zc:wchar_t", "-FS", "-Zc:rvalueCast", "-Zc:inline", "-Zc:strictStrings", "-Zc:<PERSON><PERSON><PERSON>", "-permissive-", "-Zc:__cplusplus", "-Zc:externConstexpr", "-<PERSON>i", "-MDd", "-clang:-std=c++17", "-utf-8", "-W3", "-w34100", "-w34189", "-w44456", "-w44457", "-w44458", "-wd4577", "-wd4467", "-EHsc", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "-fms-compatibility-version=19.44", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-DWIN64", "-DRC_ICONS", "-DQT_DEPRECATED_WARNINGS", "-DQT_QML_DEBUG", "-DQT_OPENGLWIDGETS_LIB", "-DQT_OPENGL_LIB", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IE:\\YingYong\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IE:\\YingYong\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\Desktop\\100\\chat-forge-master\\Client", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\Adding", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\Chatting", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\IconSetting", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\Tools", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\Rewriting", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtOpenGLWidgets", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtOpenGL", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtWidgets", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtGui", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtNetwork", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtCore", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\build\\Desktop_Qt_6_9_1_MSVC2022_64bit-Debug\\debug", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\build\\Desktop_Qt_6_9_1_MSVC2022_64bit-Debug", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:E:\\YingYong\\QT\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Tools\\MSVC\\14.44.35207\\ATLMFC\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\um", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\shared", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\winrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\cppwinrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\NETFXSDK\\4.8\\include\\um", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/TP", "D:\\Desktop\\100\\chat-forge-master\\Client\\Adding\\creategroup.h"], "directory": "D:/Desktop/100/chat-forge-master/Client/build/Desktop_Qt_6_9_1_MSVC2022_64bit-Debug/.qtc_clangd", "file": "D:/Desktop/100/chat-forge-master/Client/Adding/creategroup.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-nologo", "-Zc:wchar_t", "-FS", "-Zc:rvalueCast", "-Zc:inline", "-Zc:strictStrings", "-Zc:<PERSON><PERSON><PERSON>", "-permissive-", "-Zc:__cplusplus", "-Zc:externConstexpr", "-<PERSON>i", "-MDd", "-clang:-std=c++17", "-utf-8", "-W3", "-w34100", "-w34189", "-w44456", "-w44457", "-w44458", "-wd4577", "-wd4467", "-EHsc", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "-fms-compatibility-version=19.44", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-DWIN64", "-DRC_ICONS", "-DQT_DEPRECATED_WARNINGS", "-DQT_QML_DEBUG", "-DQT_OPENGLWIDGETS_LIB", "-DQT_OPENGL_LIB", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IE:\\YingYong\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IE:\\YingYong\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\Desktop\\100\\chat-forge-master\\Client", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\Adding", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\Chatting", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\IconSetting", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\Tools", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\Rewriting", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtOpenGLWidgets", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtOpenGL", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtWidgets", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtGui", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtNetwork", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtCore", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\build\\Desktop_Qt_6_9_1_MSVC2022_64bit-Debug\\debug", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\build\\Desktop_Qt_6_9_1_MSVC2022_64bit-Debug", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:E:\\YingYong\\QT\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Tools\\MSVC\\14.44.35207\\ATLMFC\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\um", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\shared", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\winrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\cppwinrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\NETFXSDK\\4.8\\include\\um", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/TP", "D:\\Desktop\\100\\chat-forge-master\\Client\\Adding\\systemmessage.h"], "directory": "D:/Desktop/100/chat-forge-master/Client/build/Desktop_Qt_6_9_1_MSVC2022_64bit-Debug/.qtc_clangd", "file": "D:/Desktop/100/chat-forge-master/Client/Adding/systemmessage.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-nologo", "-Zc:wchar_t", "-FS", "-Zc:rvalueCast", "-Zc:inline", "-Zc:strictStrings", "-Zc:<PERSON><PERSON><PERSON>", "-permissive-", "-Zc:__cplusplus", "-Zc:externConstexpr", "-<PERSON>i", "-MDd", "-clang:-std=c++17", "-utf-8", "-W3", "-w34100", "-w34189", "-w44456", "-w44457", "-w44458", "-wd4577", "-wd4467", "-EHsc", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "-fms-compatibility-version=19.44", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-DWIN64", "-DRC_ICONS", "-DQT_DEPRECATED_WARNINGS", "-DQT_QML_DEBUG", "-DQT_OPENGLWIDGETS_LIB", "-DQT_OPENGL_LIB", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IE:\\YingYong\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IE:\\YingYong\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\Desktop\\100\\chat-forge-master\\Client", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\Adding", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\Chatting", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\IconSetting", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\Tools", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\Rewriting", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtOpenGLWidgets", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtOpenGL", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtWidgets", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtGui", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtNetwork", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtCore", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\build\\Desktop_Qt_6_9_1_MSVC2022_64bit-Debug\\debug", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\build\\Desktop_Qt_6_9_1_MSVC2022_64bit-Debug", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:E:\\YingYong\\QT\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Tools\\MSVC\\14.44.35207\\ATLMFC\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\um", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\shared", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\winrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\cppwinrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\NETFXSDK\\4.8\\include\\um", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/TP", "D:\\Desktop\\100\\chat-forge-master\\Client\\Adding\\verificationitem.h"], "directory": "D:/Desktop/100/chat-forge-master/Client/build/Desktop_Qt_6_9_1_MSVC2022_64bit-Debug/.qtc_clangd", "file": "D:/Desktop/100/chat-forge-master/Client/Adding/verificationitem.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-nologo", "-Zc:wchar_t", "-FS", "-Zc:rvalueCast", "-Zc:inline", "-Zc:strictStrings", "-Zc:<PERSON><PERSON><PERSON>", "-permissive-", "-Zc:__cplusplus", "-Zc:externConstexpr", "-<PERSON>i", "-MDd", "-clang:-std=c++17", "-utf-8", "-W3", "-w34100", "-w34189", "-w44456", "-w44457", "-w44458", "-wd4577", "-wd4467", "-EHsc", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "-fms-compatibility-version=19.44", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-DWIN64", "-DRC_ICONS", "-DQT_DEPRECATED_WARNINGS", "-DQT_QML_DEBUG", "-DQT_OPENGLWIDGETS_LIB", "-DQT_OPENGL_LIB", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IE:\\YingYong\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IE:\\YingYong\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\Desktop\\100\\chat-forge-master\\Client", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\Adding", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\Chatting", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\IconSetting", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\Tools", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\Rewriting", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtOpenGLWidgets", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtOpenGL", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtWidgets", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtGui", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtNetwork", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtCore", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\build\\Desktop_Qt_6_9_1_MSVC2022_64bit-Debug\\debug", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\build\\Desktop_Qt_6_9_1_MSVC2022_64bit-Debug", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:E:\\YingYong\\QT\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Tools\\MSVC\\14.44.35207\\ATLMFC\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\um", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\shared", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\winrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\cppwinrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\NETFXSDK\\4.8\\include\\um", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/TP", "D:\\Desktop\\100\\chat-forge-master\\Client\\Chatting\\chatlistwidget.h"], "directory": "D:/Desktop/100/chat-forge-master/Client/build/Desktop_Qt_6_9_1_MSVC2022_64bit-Debug/.qtc_clangd", "file": "D:/Desktop/100/chat-forge-master/Client/Chatting/chatlistwidget.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-nologo", "-Zc:wchar_t", "-FS", "-Zc:rvalueCast", "-Zc:inline", "-Zc:strictStrings", "-Zc:<PERSON><PERSON><PERSON>", "-permissive-", "-Zc:__cplusplus", "-Zc:externConstexpr", "-<PERSON>i", "-MDd", "-clang:-std=c++17", "-utf-8", "-W3", "-w34100", "-w34189", "-w44456", "-w44457", "-w44458", "-wd4577", "-wd4467", "-EHsc", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "-fms-compatibility-version=19.44", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-DWIN64", "-DRC_ICONS", "-DQT_DEPRECATED_WARNINGS", "-DQT_QML_DEBUG", "-DQT_OPENGLWIDGETS_LIB", "-DQT_OPENGL_LIB", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IE:\\YingYong\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IE:\\YingYong\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\Desktop\\100\\chat-forge-master\\Client", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\Adding", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\Chatting", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\IconSetting", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\Tools", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\Rewriting", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtOpenGLWidgets", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtOpenGL", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtWidgets", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtGui", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtNetwork", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtCore", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\build\\Desktop_Qt_6_9_1_MSVC2022_64bit-Debug\\debug", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\build\\Desktop_Qt_6_9_1_MSVC2022_64bit-Debug", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:E:\\YingYong\\QT\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Tools\\MSVC\\14.44.35207\\ATLMFC\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\um", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\shared", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\winrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\cppwinrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\NETFXSDK\\4.8\\include\\um", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/TP", "D:\\Desktop\\100\\chat-forge-master\\Client\\Chatting\\chatwindow.h"], "directory": "D:/Desktop/100/chat-forge-master/Client/build/Desktop_Qt_6_9_1_MSVC2022_64bit-Debug/.qtc_clangd", "file": "D:/Desktop/100/chat-forge-master/Client/Chatting/chatwindow.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-nologo", "-Zc:wchar_t", "-FS", "-Zc:rvalueCast", "-Zc:inline", "-Zc:strictStrings", "-Zc:<PERSON><PERSON><PERSON>", "-permissive-", "-Zc:__cplusplus", "-Zc:externConstexpr", "-<PERSON>i", "-MDd", "-clang:-std=c++17", "-utf-8", "-W3", "-w34100", "-w34189", "-w44456", "-w44457", "-w44458", "-wd4577", "-wd4467", "-EHsc", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "-fms-compatibility-version=19.44", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-DWIN64", "-DRC_ICONS", "-DQT_DEPRECATED_WARNINGS", "-DQT_QML_DEBUG", "-DQT_OPENGLWIDGETS_LIB", "-DQT_OPENGL_LIB", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IE:\\YingYong\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IE:\\YingYong\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\Desktop\\100\\chat-forge-master\\Client", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\Adding", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\Chatting", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\IconSetting", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\Tools", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\Rewriting", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtOpenGLWidgets", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtOpenGL", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtWidgets", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtGui", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtNetwork", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtCore", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\build\\Desktop_Qt_6_9_1_MSVC2022_64bit-Debug\\debug", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\build\\Desktop_Qt_6_9_1_MSVC2022_64bit-Debug", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:E:\\YingYong\\QT\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Tools\\MSVC\\14.44.35207\\ATLMFC\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\um", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\shared", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\winrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\cppwinrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\NETFXSDK\\4.8\\include\\um", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/TP", "D:\\Desktop\\100\\chat-forge-master\\Client\\Chatting\\emojiselector.h"], "directory": "D:/Desktop/100/chat-forge-master/Client/build/Desktop_Qt_6_9_1_MSVC2022_64bit-Debug/.qtc_clangd", "file": "D:/Desktop/100/chat-forge-master/Client/Chatting/emojiselector.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-nologo", "-Zc:wchar_t", "-FS", "-Zc:rvalueCast", "-Zc:inline", "-Zc:strictStrings", "-Zc:<PERSON><PERSON><PERSON>", "-permissive-", "-Zc:__cplusplus", "-Zc:externConstexpr", "-<PERSON>i", "-MDd", "-clang:-std=c++17", "-utf-8", "-W3", "-w34100", "-w34189", "-w44456", "-w44457", "-w44458", "-wd4577", "-wd4467", "-EHsc", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "-fms-compatibility-version=19.44", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-DWIN64", "-DRC_ICONS", "-DQT_DEPRECATED_WARNINGS", "-DQT_QML_DEBUG", "-DQT_OPENGLWIDGETS_LIB", "-DQT_OPENGL_LIB", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IE:\\YingYong\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IE:\\YingYong\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\Desktop\\100\\chat-forge-master\\Client", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\Adding", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\Chatting", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\IconSetting", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\Tools", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\Rewriting", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtOpenGLWidgets", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtOpenGL", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtWidgets", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtGui", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtNetwork", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtCore", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\build\\Desktop_Qt_6_9_1_MSVC2022_64bit-Debug\\debug", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\build\\Desktop_Qt_6_9_1_MSVC2022_64bit-Debug", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:E:\\YingYong\\QT\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Tools\\MSVC\\14.44.35207\\ATLMFC\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\um", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\shared", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\winrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\cppwinrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\NETFXSDK\\4.8\\include\\um", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/TP", "D:\\Desktop\\100\\chat-forge-master\\Client\\IconSetting\\iconselect.h"], "directory": "D:/Desktop/100/chat-forge-master/Client/build/Desktop_Qt_6_9_1_MSVC2022_64bit-Debug/.qtc_clangd", "file": "D:/Desktop/100/chat-forge-master/Client/IconSetting/iconselect.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-nologo", "-Zc:wchar_t", "-FS", "-Zc:rvalueCast", "-Zc:inline", "-Zc:strictStrings", "-Zc:<PERSON><PERSON><PERSON>", "-permissive-", "-Zc:__cplusplus", "-Zc:externConstexpr", "-<PERSON>i", "-MDd", "-clang:-std=c++17", "-utf-8", "-W3", "-w34100", "-w34189", "-w44456", "-w44457", "-w44458", "-wd4577", "-wd4467", "-EHsc", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "-fms-compatibility-version=19.44", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-DWIN64", "-DRC_ICONS", "-DQT_DEPRECATED_WARNINGS", "-DQT_QML_DEBUG", "-DQT_OPENGLWIDGETS_LIB", "-DQT_OPENGL_LIB", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IE:\\YingYong\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IE:\\YingYong\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\Desktop\\100\\chat-forge-master\\Client", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\Adding", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\Chatting", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\IconSetting", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\Tools", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\Rewriting", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtOpenGLWidgets", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtOpenGL", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtWidgets", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtGui", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtNetwork", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtCore", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\build\\Desktop_Qt_6_9_1_MSVC2022_64bit-Debug\\debug", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\build\\Desktop_Qt_6_9_1_MSVC2022_64bit-Debug", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:E:\\YingYong\\QT\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Tools\\MSVC\\14.44.35207\\ATLMFC\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\um", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\shared", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\winrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\cppwinrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\NETFXSDK\\4.8\\include\\um", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/TP", "D:\\Desktop\\100\\chat-forge-master\\Client\\Rewriting\\friendiconlabel.h"], "directory": "D:/Desktop/100/chat-forge-master/Client/build/Desktop_Qt_6_9_1_MSVC2022_64bit-Debug/.qtc_clangd", "file": "D:/Desktop/100/chat-forge-master/Client/Rewriting/friendiconlabel.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-nologo", "-Zc:wchar_t", "-FS", "-Zc:rvalueCast", "-Zc:inline", "-Zc:strictStrings", "-Zc:<PERSON><PERSON><PERSON>", "-permissive-", "-Zc:__cplusplus", "-Zc:externConstexpr", "-<PERSON>i", "-MDd", "-clang:-std=c++17", "-utf-8", "-W3", "-w34100", "-w34189", "-w44456", "-w44457", "-w44458", "-wd4577", "-wd4467", "-EHsc", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "-fms-compatibility-version=19.44", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-DWIN64", "-DRC_ICONS", "-DQT_DEPRECATED_WARNINGS", "-DQT_QML_DEBUG", "-DQT_OPENGLWIDGETS_LIB", "-DQT_OPENGL_LIB", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IE:\\YingYong\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IE:\\YingYong\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\Desktop\\100\\chat-forge-master\\Client", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\Adding", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\Chatting", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\IconSetting", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\Tools", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\Rewriting", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtOpenGLWidgets", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtOpenGL", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtWidgets", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtGui", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtNetwork", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtCore", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\build\\Desktop_Qt_6_9_1_MSVC2022_64bit-Debug\\debug", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\build\\Desktop_Qt_6_9_1_MSVC2022_64bit-Debug", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:E:\\YingYong\\QT\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Tools\\MSVC\\14.44.35207\\ATLMFC\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\um", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\shared", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\winrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\cppwinrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\NETFXSDK\\4.8\\include\\um", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/TP", "D:\\Desktop\\100\\chat-forge-master\\Client\\Rewriting\\frienditem.h"], "directory": "D:/Desktop/100/chat-forge-master/Client/build/Desktop_Qt_6_9_1_MSVC2022_64bit-Debug/.qtc_clangd", "file": "D:/Desktop/100/chat-forge-master/Client/Rewriting/frienditem.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-nologo", "-Zc:wchar_t", "-FS", "-Zc:rvalueCast", "-Zc:inline", "-Zc:strictStrings", "-Zc:<PERSON><PERSON><PERSON>", "-permissive-", "-Zc:__cplusplus", "-Zc:externConstexpr", "-<PERSON>i", "-MDd", "-clang:-std=c++17", "-utf-8", "-W3", "-w34100", "-w34189", "-w44456", "-w44457", "-w44458", "-wd4577", "-wd4467", "-EHsc", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "-fms-compatibility-version=19.44", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-DWIN64", "-DRC_ICONS", "-DQT_DEPRECATED_WARNINGS", "-DQT_QML_DEBUG", "-DQT_OPENGLWIDGETS_LIB", "-DQT_OPENGL_LIB", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IE:\\YingYong\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IE:\\YingYong\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\Desktop\\100\\chat-forge-master\\Client", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\Adding", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\Chatting", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\IconSetting", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\Tools", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\Rewriting", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtOpenGLWidgets", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtOpenGL", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtWidgets", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtGui", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtNetwork", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtCore", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\build\\Desktop_Qt_6_9_1_MSVC2022_64bit-Debug\\debug", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\build\\Desktop_Qt_6_9_1_MSVC2022_64bit-Debug", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:E:\\YingYong\\QT\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Tools\\MSVC\\14.44.35207\\ATLMFC\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\um", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\shared", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\winrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\cppwinrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\NETFXSDK\\4.8\\include\\um", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/TP", "D:\\Desktop\\100\\chat-forge-master\\Client\\Rewriting\\sendtextedit.h"], "directory": "D:/Desktop/100/chat-forge-master/Client/build/Desktop_Qt_6_9_1_MSVC2022_64bit-Debug/.qtc_clangd", "file": "D:/Desktop/100/chat-forge-master/Client/Rewriting/sendtextedit.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-nologo", "-Zc:wchar_t", "-FS", "-Zc:rvalueCast", "-Zc:inline", "-Zc:strictStrings", "-Zc:<PERSON><PERSON><PERSON>", "-permissive-", "-Zc:__cplusplus", "-Zc:externConstexpr", "-<PERSON>i", "-MDd", "-clang:-std=c++17", "-utf-8", "-W3", "-w34100", "-w34189", "-w44456", "-w44457", "-w44458", "-wd4577", "-wd4467", "-EHsc", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "-fms-compatibility-version=19.44", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-DWIN64", "-DRC_ICONS", "-DQT_DEPRECATED_WARNINGS", "-DQT_QML_DEBUG", "-DQT_OPENGLWIDGETS_LIB", "-DQT_OPENGL_LIB", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IE:\\YingYong\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IE:\\YingYong\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\Desktop\\100\\chat-forge-master\\Client", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\Adding", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\Chatting", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\IconSetting", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\Tools", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\Rewriting", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtOpenGLWidgets", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtOpenGL", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtWidgets", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtGui", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtNetwork", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtCore", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\build\\Desktop_Qt_6_9_1_MSVC2022_64bit-Debug\\debug", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\build\\Desktop_Qt_6_9_1_MSVC2022_64bit-Debug", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:E:\\YingYong\\QT\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Tools\\MSVC\\14.44.35207\\ATLMFC\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\um", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\shared", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\winrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\cppwinrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\NETFXSDK\\4.8\\include\\um", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/TP", "D:\\Desktop\\100\\chat-forge-master\\Client\\Rewriting\\smoothscrolllistwidget.h"], "directory": "D:/Desktop/100/chat-forge-master/Client/build/Desktop_Qt_6_9_1_MSVC2022_64bit-Debug/.qtc_clangd", "file": "D:/Desktop/100/chat-forge-master/Client/Rewriting/smoothscrolllistwidget.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-nologo", "-Zc:wchar_t", "-FS", "-Zc:rvalueCast", "-Zc:inline", "-Zc:strictStrings", "-Zc:<PERSON><PERSON><PERSON>", "-permissive-", "-Zc:__cplusplus", "-Zc:externConstexpr", "-<PERSON>i", "-MDd", "-clang:-std=c++17", "-utf-8", "-W3", "-w34100", "-w34189", "-w44456", "-w44457", "-w44458", "-wd4577", "-wd4467", "-EHsc", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "-fms-compatibility-version=19.44", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-DWIN64", "-DRC_ICONS", "-DQT_DEPRECATED_WARNINGS", "-DQT_QML_DEBUG", "-DQT_OPENGLWIDGETS_LIB", "-DQT_OPENGL_LIB", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IE:\\YingYong\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IE:\\YingYong\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\Desktop\\100\\chat-forge-master\\Client", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\Adding", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\Chatting", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\IconSetting", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\Tools", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\Rewriting", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtOpenGLWidgets", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtOpenGL", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtWidgets", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtGui", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtNetwork", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtCore", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\build\\Desktop_Qt_6_9_1_MSVC2022_64bit-Debug\\debug", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\build\\Desktop_Qt_6_9_1_MSVC2022_64bit-Debug", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:E:\\YingYong\\QT\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Tools\\MSVC\\14.44.35207\\ATLMFC\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\um", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\shared", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\winrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\cppwinrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\NETFXSDK\\4.8\\include\\um", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/TP", "D:\\Desktop\\100\\chat-forge-master\\Client\\Tools\\imagepreview.h"], "directory": "D:/Desktop/100/chat-forge-master/Client/build/Desktop_Qt_6_9_1_MSVC2022_64bit-Debug/.qtc_clangd", "file": "D:/Desktop/100/chat-forge-master/Client/Tools/imagepreview.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-nologo", "-Zc:wchar_t", "-FS", "-Zc:rvalueCast", "-Zc:inline", "-Zc:strictStrings", "-Zc:<PERSON><PERSON><PERSON>", "-permissive-", "-Zc:__cplusplus", "-Zc:externConstexpr", "-<PERSON>i", "-MDd", "-clang:-std=c++17", "-utf-8", "-W3", "-w34100", "-w34189", "-w44456", "-w44457", "-w44458", "-wd4577", "-wd4467", "-EHsc", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "-fms-compatibility-version=19.44", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-DWIN64", "-DRC_ICONS", "-DQT_DEPRECATED_WARNINGS", "-DQT_QML_DEBUG", "-DQT_OPENGLWIDGETS_LIB", "-DQT_OPENGL_LIB", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IE:\\YingYong\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IE:\\YingYong\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\Desktop\\100\\chat-forge-master\\Client", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\Adding", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\Chatting", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\IconSetting", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\Tools", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\Rewriting", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtOpenGLWidgets", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtOpenGL", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtWidgets", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtGui", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtNetwork", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtCore", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\build\\Desktop_Qt_6_9_1_MSVC2022_64bit-Debug\\debug", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\build\\Desktop_Qt_6_9_1_MSVC2022_64bit-Debug", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:E:\\YingYong\\QT\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Tools\\MSVC\\14.44.35207\\ATLMFC\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\um", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\shared", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\winrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\cppwinrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\NETFXSDK\\4.8\\include\\um", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/TP", "D:\\Desktop\\100\\chat-forge-master\\Client\\Tools\\stringtool.h"], "directory": "D:/Desktop/100/chat-forge-master/Client/build/Desktop_Qt_6_9_1_MSVC2022_64bit-Debug/.qtc_clangd", "file": "D:/Desktop/100/chat-forge-master/Client/Tools/stringtool.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-nologo", "-Zc:wchar_t", "-FS", "-Zc:rvalueCast", "-Zc:inline", "-Zc:strictStrings", "-Zc:<PERSON><PERSON><PERSON>", "-permissive-", "-Zc:__cplusplus", "-Zc:externConstexpr", "-<PERSON>i", "-MDd", "-clang:-std=c++17", "-utf-8", "-W3", "-w34100", "-w34189", "-w44456", "-w44457", "-w44458", "-wd4577", "-wd4467", "-EHsc", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "-fms-compatibility-version=19.44", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-DWIN64", "-DRC_ICONS", "-DQT_DEPRECATED_WARNINGS", "-DQT_QML_DEBUG", "-DQT_OPENGLWIDGETS_LIB", "-DQT_OPENGL_LIB", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IE:\\YingYong\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IE:\\YingYong\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\Desktop\\100\\chat-forge-master\\Client", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\Adding", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\Chatting", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\IconSetting", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\Tools", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\Rewriting", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtOpenGLWidgets", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtOpenGL", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtWidgets", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtGui", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtNetwork", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtCore", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\build\\Desktop_Qt_6_9_1_MSVC2022_64bit-Debug\\debug", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\build\\Desktop_Qt_6_9_1_MSVC2022_64bit-Debug", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:E:\\YingYong\\QT\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Tools\\MSVC\\14.44.35207\\ATLMFC\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\um", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\shared", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\winrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\cppwinrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\NETFXSDK\\4.8\\include\\um", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/TP", "D:\\Desktop\\100\\chat-forge-master\\Client\\Tools\\tcpclient.h"], "directory": "D:/Desktop/100/chat-forge-master/Client/build/Desktop_Qt_6_9_1_MSVC2022_64bit-Debug/.qtc_clangd", "file": "D:/Desktop/100/chat-forge-master/Client/Tools/tcpclient.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-nologo", "-Zc:wchar_t", "-FS", "-Zc:rvalueCast", "-Zc:inline", "-Zc:strictStrings", "-Zc:<PERSON><PERSON><PERSON>", "-permissive-", "-Zc:__cplusplus", "-Zc:externConstexpr", "-<PERSON>i", "-MDd", "-clang:-std=c++17", "-utf-8", "-W3", "-w34100", "-w34189", "-w44456", "-w44457", "-w44458", "-wd4577", "-wd4467", "-EHsc", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "-fms-compatibility-version=19.44", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-DWIN64", "-DRC_ICONS", "-DQT_DEPRECATED_WARNINGS", "-DQT_QML_DEBUG", "-DQT_OPENGLWIDGETS_LIB", "-DQT_OPENGL_LIB", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IE:\\YingYong\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IE:\\YingYong\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\Desktop\\100\\chat-forge-master\\Client", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\Adding", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\Chatting", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\IconSetting", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\Tools", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\Rewriting", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtOpenGLWidgets", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtOpenGL", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtWidgets", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtGui", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtNetwork", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtCore", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\build\\Desktop_Qt_6_9_1_MSVC2022_64bit-Debug\\debug", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\build\\Desktop_Qt_6_9_1_MSVC2022_64bit-Debug", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:E:\\YingYong\\QT\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Tools\\MSVC\\14.44.35207\\ATLMFC\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\um", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\shared", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\winrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\cppwinrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\NETFXSDK\\4.8\\include\\um", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/TP", "D:\\Desktop\\100\\chat-forge-master\\Client\\client.h"], "directory": "D:/Desktop/100/chat-forge-master/Client/build/Desktop_Qt_6_9_1_MSVC2022_64bit-Debug/.qtc_clangd", "file": "D:/Desktop/100/chat-forge-master/Client/client.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-nologo", "-Zc:wchar_t", "-FS", "-Zc:rvalueCast", "-Zc:inline", "-Zc:strictStrings", "-Zc:<PERSON><PERSON><PERSON>", "-permissive-", "-Zc:__cplusplus", "-Zc:externConstexpr", "-<PERSON>i", "-MDd", "-clang:-std=c++17", "-utf-8", "-W3", "-w34100", "-w34189", "-w44456", "-w44457", "-w44458", "-wd4577", "-wd4467", "-EHsc", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "-fms-compatibility-version=19.44", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-DWIN64", "-DRC_ICONS", "-DQT_DEPRECATED_WARNINGS", "-DQT_QML_DEBUG", "-DQT_OPENGLWIDGETS_LIB", "-DQT_OPENGL_LIB", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IE:\\YingYong\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IE:\\YingYong\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\Desktop\\100\\chat-forge-master\\Client", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\Adding", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\Chatting", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\IconSetting", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\Tools", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\Rewriting", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtOpenGLWidgets", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtOpenGL", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtWidgets", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtGui", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtNetwork", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtCore", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\build\\Desktop_Qt_6_9_1_MSVC2022_64bit-Debug\\debug", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\build\\Desktop_Qt_6_9_1_MSVC2022_64bit-Debug", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:E:\\YingYong\\QT\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Tools\\MSVC\\14.44.35207\\ATLMFC\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\um", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\shared", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\winrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\cppwinrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\NETFXSDK\\4.8\\include\\um", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/TP", "D:\\Desktop\\100\\chat-forge-master\\Client\\friendinfowidget.h"], "directory": "D:/Desktop/100/chat-forge-master/Client/build/Desktop_Qt_6_9_1_MSVC2022_64bit-Debug/.qtc_clangd", "file": "D:/Desktop/100/chat-forge-master/Client/friendinfowidget.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-nologo", "-Zc:wchar_t", "-FS", "-Zc:rvalueCast", "-Zc:inline", "-Zc:strictStrings", "-Zc:<PERSON><PERSON><PERSON>", "-permissive-", "-Zc:__cplusplus", "-Zc:externConstexpr", "-<PERSON>i", "-MDd", "-clang:-std=c++17", "-utf-8", "-W3", "-w34100", "-w34189", "-w44456", "-w44457", "-w44458", "-wd4577", "-wd4467", "-EHsc", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "-fms-compatibility-version=19.44", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-DWIN64", "-DRC_ICONS", "-DQT_DEPRECATED_WARNINGS", "-DQT_QML_DEBUG", "-DQT_OPENGLWIDGETS_LIB", "-DQT_OPENGL_LIB", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IE:\\YingYong\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IE:\\YingYong\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\Desktop\\100\\chat-forge-master\\Client", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\Adding", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\Chatting", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\IconSetting", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\Tools", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\Rewriting", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtOpenGLWidgets", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtOpenGL", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtWidgets", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtGui", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtNetwork", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtCore", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\build\\Desktop_Qt_6_9_1_MSVC2022_64bit-Debug\\debug", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\build\\Desktop_Qt_6_9_1_MSVC2022_64bit-Debug", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:E:\\YingYong\\QT\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Tools\\MSVC\\14.44.35207\\ATLMFC\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\um", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\shared", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\winrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\cppwinrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\NETFXSDK\\4.8\\include\\um", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/TP", "D:\\Desktop\\100\\chat-forge-master\\Client\\logging.h"], "directory": "D:/Desktop/100/chat-forge-master/Client/build/Desktop_Qt_6_9_1_MSVC2022_64bit-Debug/.qtc_clangd", "file": "D:/Desktop/100/chat-forge-master/Client/logging.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-nologo", "-Zc:wchar_t", "-FS", "-Zc:rvalueCast", "-Zc:inline", "-Zc:strictStrings", "-Zc:<PERSON><PERSON><PERSON>", "-permissive-", "-Zc:__cplusplus", "-Zc:externConstexpr", "-<PERSON>i", "-MDd", "-clang:-std=c++17", "-utf-8", "-W3", "-w34100", "-w34189", "-w44456", "-w44457", "-w44458", "-wd4577", "-wd4467", "-EHsc", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "-fms-compatibility-version=19.44", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-DWIN64", "-DRC_ICONS", "-DQT_DEPRECATED_WARNINGS", "-DQT_QML_DEBUG", "-DQT_OPENGLWIDGETS_LIB", "-DQT_OPENGL_LIB", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IE:\\YingYong\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IE:\\YingYong\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\Desktop\\100\\chat-forge-master\\Client", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\Adding", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\Chatting", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\IconSetting", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\Tools", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\Rewriting", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtOpenGLWidgets", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtOpenGL", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtWidgets", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtGui", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtNetwork", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtCore", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\build\\Desktop_Qt_6_9_1_MSVC2022_64bit-Debug\\debug", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\build\\Desktop_Qt_6_9_1_MSVC2022_64bit-Debug", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:E:\\YingYong\\QT\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Tools\\MSVC\\14.44.35207\\ATLMFC\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\um", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\shared", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\winrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\cppwinrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\NETFXSDK\\4.8\\include\\um", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/TP", "D:\\Desktop\\100\\chat-forge-master\\Client\\selfinfowidget.h"], "directory": "D:/Desktop/100/chat-forge-master/Client/build/Desktop_Qt_6_9_1_MSVC2022_64bit-Debug/.qtc_clangd", "file": "D:/Desktop/100/chat-forge-master/Client/selfinfowidget.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-nologo", "-Zc:wchar_t", "-FS", "-Zc:rvalueCast", "-Zc:inline", "-Zc:strictStrings", "-Zc:<PERSON><PERSON><PERSON>", "-permissive-", "-Zc:__cplusplus", "-Zc:externConstexpr", "-<PERSON>i", "-MDd", "-clang:-std=c++17", "-utf-8", "-W3", "-w34100", "-w34189", "-w44456", "-w44457", "-w44458", "-wd4577", "-wd4467", "-EHsc", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "-fms-compatibility-version=19.44", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-DWIN64", "-DRC_ICONS", "-DQT_DEPRECATED_WARNINGS", "-DQT_QML_DEBUG", "-DQT_OPENGLWIDGETS_LIB", "-DQT_OPENGL_LIB", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IE:\\YingYong\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IE:\\YingYong\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\Desktop\\100\\chat-forge-master\\Client", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\Adding", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\Chatting", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\IconSetting", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\Tools", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\Rewriting", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtOpenGLWidgets", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtOpenGL", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtWidgets", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtGui", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtNetwork", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtCore", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\build\\Desktop_Qt_6_9_1_MSVC2022_64bit-Debug\\debug", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\build\\Desktop_Qt_6_9_1_MSVC2022_64bit-Debug", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:E:\\YingYong\\QT\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Tools\\MSVC\\14.44.35207\\ATLMFC\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\um", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\shared", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\winrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\cppwinrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\NETFXSDK\\4.8\\include\\um", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/TP", "D:\\Desktop\\100\\chat-forge-master\\Client\\build\\Desktop_Qt_6_9_1_MSVC2022_64bit-Debug\\ui_chatwindow.h"], "directory": "D:/Desktop/100/chat-forge-master/Client/build/Desktop_Qt_6_9_1_MSVC2022_64bit-Debug/.qtc_clangd", "file": "D:/Desktop/100/chat-forge-master/Client/build/Desktop_Qt_6_9_1_MSVC2022_64bit-Debug/ui_chatwindow.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-nologo", "-Zc:wchar_t", "-FS", "-Zc:rvalueCast", "-Zc:inline", "-Zc:strictStrings", "-Zc:<PERSON><PERSON><PERSON>", "-permissive-", "-Zc:__cplusplus", "-Zc:externConstexpr", "-<PERSON>i", "-MDd", "-clang:-std=c++17", "-utf-8", "-W3", "-w34100", "-w34189", "-w44456", "-w44457", "-w44458", "-wd4577", "-wd4467", "-EHsc", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "-fms-compatibility-version=19.44", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-DWIN64", "-DRC_ICONS", "-DQT_DEPRECATED_WARNINGS", "-DQT_QML_DEBUG", "-DQT_OPENGLWIDGETS_LIB", "-DQT_OPENGL_LIB", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IE:\\YingYong\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IE:\\YingYong\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\Desktop\\100\\chat-forge-master\\Client", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\Adding", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\Chatting", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\IconSetting", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\Tools", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\Rewriting", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtOpenGLWidgets", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtOpenGL", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtWidgets", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtGui", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtNetwork", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtCore", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\build\\Desktop_Qt_6_9_1_MSVC2022_64bit-Debug\\debug", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\build\\Desktop_Qt_6_9_1_MSVC2022_64bit-Debug", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:E:\\YingYong\\QT\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Tools\\MSVC\\14.44.35207\\ATLMFC\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\um", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\shared", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\winrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\cppwinrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\NETFXSDK\\4.8\\include\\um", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/TP", "D:\\Desktop\\100\\chat-forge-master\\Client\\build\\Desktop_Qt_6_9_1_MSVC2022_64bit-Debug\\ui_frienditem.h"], "directory": "D:/Desktop/100/chat-forge-master/Client/build/Desktop_Qt_6_9_1_MSVC2022_64bit-Debug/.qtc_clangd", "file": "D:/Desktop/100/chat-forge-master/Client/build/Desktop_Qt_6_9_1_MSVC2022_64bit-Debug/ui_frienditem.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-nologo", "-Zc:wchar_t", "-FS", "-Zc:rvalueCast", "-Zc:inline", "-Zc:strictStrings", "-Zc:<PERSON><PERSON><PERSON>", "-permissive-", "-Zc:__cplusplus", "-Zc:externConstexpr", "-<PERSON>i", "-MDd", "-clang:-std=c++17", "-utf-8", "-W3", "-w34100", "-w34189", "-w44456", "-w44457", "-w44458", "-wd4577", "-wd4467", "-EHsc", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "-fms-compatibility-version=19.44", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-DWIN64", "-DRC_ICONS", "-DQT_DEPRECATED_WARNINGS", "-DQT_QML_DEBUG", "-DQT_OPENGLWIDGETS_LIB", "-DQT_OPENGL_LIB", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IE:\\YingYong\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IE:\\YingYong\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\Desktop\\100\\chat-forge-master\\Client", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\Adding", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\Chatting", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\IconSetting", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\Tools", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\Rewriting", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtOpenGLWidgets", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtOpenGL", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtWidgets", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtGui", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtNetwork", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtCore", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\build\\Desktop_Qt_6_9_1_MSVC2022_64bit-Debug\\debug", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\build\\Desktop_Qt_6_9_1_MSVC2022_64bit-Debug", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:E:\\YingYong\\QT\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Tools\\MSVC\\14.44.35207\\ATLMFC\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\um", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\shared", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\winrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\cppwinrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\NETFXSDK\\4.8\\include\\um", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/TP", "D:\\Desktop\\100\\chat-forge-master\\Client\\build\\Desktop_Qt_6_9_1_MSVC2022_64bit-Debug\\ui_selfinfowidget.h"], "directory": "D:/Desktop/100/chat-forge-master/Client/build/Desktop_Qt_6_9_1_MSVC2022_64bit-Debug/.qtc_clangd", "file": "D:/Desktop/100/chat-forge-master/Client/build/Desktop_Qt_6_9_1_MSVC2022_64bit-Debug/ui_selfinfowidget.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-nologo", "-Zc:wchar_t", "-FS", "-Zc:rvalueCast", "-Zc:inline", "-Zc:strictStrings", "-Zc:<PERSON><PERSON><PERSON>", "-permissive-", "-Zc:__cplusplus", "-Zc:externConstexpr", "-<PERSON>i", "-MDd", "-clang:-std=c++17", "-utf-8", "-W3", "-w34100", "-w34189", "-w44456", "-w44457", "-w44458", "-wd4577", "-wd4467", "-EHsc", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "-fms-compatibility-version=19.44", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-DWIN64", "-DRC_ICONS", "-DQT_DEPRECATED_WARNINGS", "-DQT_QML_DEBUG", "-DQT_OPENGLWIDGETS_LIB", "-DQT_OPENGL_LIB", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IE:\\YingYong\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IE:\\YingYong\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\Desktop\\100\\chat-forge-master\\Client", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\Adding", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\Chatting", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\IconSetting", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\Tools", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\Rewriting", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtOpenGLWidgets", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtOpenGL", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtWidgets", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtGui", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtNetwork", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtCore", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\build\\Desktop_Qt_6_9_1_MSVC2022_64bit-Debug\\debug", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\build\\Desktop_Qt_6_9_1_MSVC2022_64bit-Debug", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:E:\\YingYong\\QT\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Tools\\MSVC\\14.44.35207\\ATLMFC\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\um", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\shared", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\winrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\cppwinrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\NETFXSDK\\4.8\\include\\um", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/TP", "D:\\Desktop\\100\\chat-forge-master\\Client\\build\\Desktop_Qt_6_9_1_MSVC2022_64bit-Debug\\ui_friendinfowidget.h"], "directory": "D:/Desktop/100/chat-forge-master/Client/build/Desktop_Qt_6_9_1_MSVC2022_64bit-Debug/.qtc_clangd", "file": "D:/Desktop/100/chat-forge-master/Client/build/Desktop_Qt_6_9_1_MSVC2022_64bit-Debug/ui_friendinfowidget.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-nologo", "-Zc:wchar_t", "-FS", "-Zc:rvalueCast", "-Zc:inline", "-Zc:strictStrings", "-Zc:<PERSON><PERSON><PERSON>", "-permissive-", "-Zc:__cplusplus", "-Zc:externConstexpr", "-<PERSON>i", "-MDd", "-clang:-std=c++17", "-utf-8", "-W3", "-w34100", "-w34189", "-w44456", "-w44457", "-w44458", "-wd4577", "-wd4467", "-EHsc", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "-fms-compatibility-version=19.44", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-DWIN64", "-DRC_ICONS", "-DQT_DEPRECATED_WARNINGS", "-DQT_QML_DEBUG", "-DQT_OPENGLWIDGETS_LIB", "-DQT_OPENGL_LIB", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IE:\\YingYong\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IE:\\YingYong\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\Desktop\\100\\chat-forge-master\\Client", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\Adding", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\Chatting", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\IconSetting", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\Tools", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\Rewriting", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtOpenGLWidgets", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtOpenGL", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtWidgets", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtGui", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtNetwork", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtCore", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\build\\Desktop_Qt_6_9_1_MSVC2022_64bit-Debug\\debug", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\build\\Desktop_Qt_6_9_1_MSVC2022_64bit-Debug", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:E:\\YingYong\\QT\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Tools\\MSVC\\14.44.35207\\ATLMFC\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\um", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\shared", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\winrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\cppwinrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\NETFXSDK\\4.8\\include\\um", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/TP", "D:\\Desktop\\100\\chat-forge-master\\Client\\build\\Desktop_Qt_6_9_1_MSVC2022_64bit-Debug\\ui_imagepreview.h"], "directory": "D:/Desktop/100/chat-forge-master/Client/build/Desktop_Qt_6_9_1_MSVC2022_64bit-Debug/.qtc_clangd", "file": "D:/Desktop/100/chat-forge-master/Client/build/Desktop_Qt_6_9_1_MSVC2022_64bit-Debug/ui_imagepreview.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-nologo", "-Zc:wchar_t", "-FS", "-Zc:rvalueCast", "-Zc:inline", "-Zc:strictStrings", "-Zc:<PERSON><PERSON><PERSON>", "-permissive-", "-Zc:__cplusplus", "-Zc:externConstexpr", "-<PERSON>i", "-MDd", "-clang:-std=c++17", "-utf-8", "-W3", "-w34100", "-w34189", "-w44456", "-w44457", "-w44458", "-wd4577", "-wd4467", "-EHsc", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "-fms-compatibility-version=19.44", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-DWIN64", "-DRC_ICONS", "-DQT_DEPRECATED_WARNINGS", "-DQT_QML_DEBUG", "-DQT_OPENGLWIDGETS_LIB", "-DQT_OPENGL_LIB", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IE:\\YingYong\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IE:\\YingYong\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\Desktop\\100\\chat-forge-master\\Client", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\Adding", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\Chatting", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\IconSetting", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\Tools", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\Rewriting", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtOpenGLWidgets", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtOpenGL", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtWidgets", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtGui", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtNetwork", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtCore", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\build\\Desktop_Qt_6_9_1_MSVC2022_64bit-Debug\\debug", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\build\\Desktop_Qt_6_9_1_MSVC2022_64bit-Debug", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:E:\\YingYong\\QT\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Tools\\MSVC\\14.44.35207\\ATLMFC\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\um", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\shared", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\winrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\cppwinrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\NETFXSDK\\4.8\\include\\um", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/TP", "D:\\Desktop\\100\\chat-forge-master\\Client\\build\\Desktop_Qt_6_9_1_MSVC2022_64bit-Debug\\ui_client.h"], "directory": "D:/Desktop/100/chat-forge-master/Client/build/Desktop_Qt_6_9_1_MSVC2022_64bit-Debug/.qtc_clangd", "file": "D:/Desktop/100/chat-forge-master/Client/build/Desktop_Qt_6_9_1_MSVC2022_64bit-Debug/ui_client.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-nologo", "-Zc:wchar_t", "-FS", "-Zc:rvalueCast", "-Zc:inline", "-Zc:strictStrings", "-Zc:<PERSON><PERSON><PERSON>", "-permissive-", "-Zc:__cplusplus", "-Zc:externConstexpr", "-<PERSON>i", "-MDd", "-clang:-std=c++17", "-utf-8", "-W3", "-w34100", "-w34189", "-w44456", "-w44457", "-w44458", "-wd4577", "-wd4467", "-EHsc", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "-fms-compatibility-version=19.44", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-DWIN64", "-DRC_ICONS", "-DQT_DEPRECATED_WARNINGS", "-DQT_QML_DEBUG", "-DQT_OPENGLWIDGETS_LIB", "-DQT_OPENGL_LIB", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IE:\\YingYong\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IE:\\YingYong\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\Desktop\\100\\chat-forge-master\\Client", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\Adding", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\Chatting", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\IconSetting", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\Tools", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\Rewriting", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtOpenGLWidgets", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtOpenGL", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtWidgets", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtGui", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtNetwork", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtCore", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\build\\Desktop_Qt_6_9_1_MSVC2022_64bit-Debug\\debug", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\build\\Desktop_Qt_6_9_1_MSVC2022_64bit-Debug", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:E:\\YingYong\\QT\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Tools\\MSVC\\14.44.35207\\ATLMFC\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\um", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\shared", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\winrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\cppwinrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\NETFXSDK\\4.8\\include\\um", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/TP", "D:\\Desktop\\100\\chat-forge-master\\Client\\build\\Desktop_Qt_6_9_1_MSVC2022_64bit-Debug\\ui_creategroup.h"], "directory": "D:/Desktop/100/chat-forge-master/Client/build/Desktop_Qt_6_9_1_MSVC2022_64bit-Debug/.qtc_clangd", "file": "D:/Desktop/100/chat-forge-master/Client/build/Desktop_Qt_6_9_1_MSVC2022_64bit-Debug/ui_creategroup.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-nologo", "-Zc:wchar_t", "-FS", "-Zc:rvalueCast", "-Zc:inline", "-Zc:strictStrings", "-Zc:<PERSON><PERSON><PERSON>", "-permissive-", "-Zc:__cplusplus", "-Zc:externConstexpr", "-<PERSON>i", "-MDd", "-clang:-std=c++17", "-utf-8", "-W3", "-w34100", "-w34189", "-w44456", "-w44457", "-w44458", "-wd4577", "-wd4467", "-EHsc", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "-fms-compatibility-version=19.44", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-DWIN64", "-DRC_ICONS", "-DQT_DEPRECATED_WARNINGS", "-DQT_QML_DEBUG", "-DQT_OPENGLWIDGETS_LIB", "-DQT_OPENGL_LIB", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IE:\\YingYong\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IE:\\YingYong\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\Desktop\\100\\chat-forge-master\\Client", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\Adding", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\Chatting", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\IconSetting", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\Tools", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\Rewriting", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtOpenGLWidgets", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtOpenGL", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtWidgets", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtGui", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtNetwork", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtCore", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\build\\Desktop_Qt_6_9_1_MSVC2022_64bit-Debug\\debug", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\build\\Desktop_Qt_6_9_1_MSVC2022_64bit-Debug", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:E:\\YingYong\\QT\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Tools\\MSVC\\14.44.35207\\ATLMFC\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\um", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\shared", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\winrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\cppwinrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\NETFXSDK\\4.8\\include\\um", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/TP", "D:\\Desktop\\100\\chat-forge-master\\Client\\build\\Desktop_Qt_6_9_1_MSVC2022_64bit-Debug\\ui_verificationitem.h"], "directory": "D:/Desktop/100/chat-forge-master/Client/build/Desktop_Qt_6_9_1_MSVC2022_64bit-Debug/.qtc_clangd", "file": "D:/Desktop/100/chat-forge-master/Client/build/Desktop_Qt_6_9_1_MSVC2022_64bit-Debug/ui_verificationitem.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-nologo", "-Zc:wchar_t", "-FS", "-Zc:rvalueCast", "-Zc:inline", "-Zc:strictStrings", "-Zc:<PERSON><PERSON><PERSON>", "-permissive-", "-Zc:__cplusplus", "-Zc:externConstexpr", "-<PERSON>i", "-MDd", "-clang:-std=c++17", "-utf-8", "-W3", "-w34100", "-w34189", "-w44456", "-w44457", "-w44458", "-wd4577", "-wd4467", "-EHsc", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "-fms-compatibility-version=19.44", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-DWIN64", "-DRC_ICONS", "-DQT_DEPRECATED_WARNINGS", "-DQT_QML_DEBUG", "-DQT_OPENGLWIDGETS_LIB", "-DQT_OPENGL_LIB", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IE:\\YingYong\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IE:\\YingYong\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\Desktop\\100\\chat-forge-master\\Client", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\Adding", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\Chatting", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\IconSetting", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\Tools", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\Rewriting", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtOpenGLWidgets", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtOpenGL", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtWidgets", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtGui", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtNetwork", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtCore", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\build\\Desktop_Qt_6_9_1_MSVC2022_64bit-Debug\\debug", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\build\\Desktop_Qt_6_9_1_MSVC2022_64bit-Debug", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:E:\\YingYong\\QT\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Tools\\MSVC\\14.44.35207\\ATLMFC\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\um", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\shared", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\winrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\cppwinrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\NETFXSDK\\4.8\\include\\um", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/TP", "D:\\Desktop\\100\\chat-forge-master\\Client\\build\\Desktop_Qt_6_9_1_MSVC2022_64bit-Debug\\ui_iconselect.h"], "directory": "D:/Desktop/100/chat-forge-master/Client/build/Desktop_Qt_6_9_1_MSVC2022_64bit-Debug/.qtc_clangd", "file": "D:/Desktop/100/chat-forge-master/Client/build/Desktop_Qt_6_9_1_MSVC2022_64bit-Debug/ui_iconselect.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-nologo", "-Zc:wchar_t", "-FS", "-Zc:rvalueCast", "-Zc:inline", "-Zc:strictStrings", "-Zc:<PERSON><PERSON><PERSON>", "-permissive-", "-Zc:__cplusplus", "-Zc:externConstexpr", "-<PERSON>i", "-MDd", "-clang:-std=c++17", "-utf-8", "-W3", "-w34100", "-w34189", "-w44456", "-w44457", "-w44458", "-wd4577", "-wd4467", "-EHsc", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "-fms-compatibility-version=19.44", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-DWIN64", "-DRC_ICONS", "-DQT_DEPRECATED_WARNINGS", "-DQT_QML_DEBUG", "-DQT_OPENGLWIDGETS_LIB", "-DQT_OPENGL_LIB", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IE:\\YingYong\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IE:\\YingYong\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\Desktop\\100\\chat-forge-master\\Client", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\Adding", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\Chatting", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\IconSetting", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\Tools", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\Rewriting", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtOpenGLWidgets", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtOpenGL", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtWidgets", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtGui", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtNetwork", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtCore", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\build\\Desktop_Qt_6_9_1_MSVC2022_64bit-Debug\\debug", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\build\\Desktop_Qt_6_9_1_MSVC2022_64bit-Debug", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:E:\\YingYong\\QT\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Tools\\MSVC\\14.44.35207\\ATLMFC\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\um", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\shared", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\winrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\cppwinrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\NETFXSDK\\4.8\\include\\um", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/TP", "D:\\Desktop\\100\\chat-forge-master\\Client\\build\\Desktop_Qt_6_9_1_MSVC2022_64bit-Debug\\ui_systemmessage.h"], "directory": "D:/Desktop/100/chat-forge-master/Client/build/Desktop_Qt_6_9_1_MSVC2022_64bit-Debug/.qtc_clangd", "file": "D:/Desktop/100/chat-forge-master/Client/build/Desktop_Qt_6_9_1_MSVC2022_64bit-Debug/ui_systemmessage.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-nologo", "-Zc:wchar_t", "-FS", "-Zc:rvalueCast", "-Zc:inline", "-Zc:strictStrings", "-Zc:<PERSON><PERSON><PERSON>", "-permissive-", "-Zc:__cplusplus", "-Zc:externConstexpr", "-<PERSON>i", "-MDd", "-clang:-std=c++17", "-utf-8", "-W3", "-w34100", "-w34189", "-w44456", "-w44457", "-w44458", "-wd4577", "-wd4467", "-EHsc", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "-fms-compatibility-version=19.44", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-DWIN64", "-DRC_ICONS", "-DQT_DEPRECATED_WARNINGS", "-DQT_QML_DEBUG", "-DQT_OPENGLWIDGETS_LIB", "-DQT_OPENGL_LIB", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IE:\\YingYong\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IE:\\YingYong\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\Desktop\\100\\chat-forge-master\\Client", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\Adding", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\Chatting", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\IconSetting", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\Tools", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\Rewriting", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtOpenGLWidgets", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtOpenGL", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtWidgets", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtGui", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtNetwork", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtCore", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\build\\Desktop_Qt_6_9_1_MSVC2022_64bit-Debug\\debug", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\build\\Desktop_Qt_6_9_1_MSVC2022_64bit-Debug", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:E:\\YingYong\\QT\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Tools\\MSVC\\14.44.35207\\ATLMFC\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\um", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\shared", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\winrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\cppwinrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\NETFXSDK\\4.8\\include\\um", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/TP", "D:\\Desktop\\100\\chat-forge-master\\Client\\build\\Desktop_Qt_6_9_1_MSVC2022_64bit-Debug\\ui_addfriend.h"], "directory": "D:/Desktop/100/chat-forge-master/Client/build/Desktop_Qt_6_9_1_MSVC2022_64bit-Debug/.qtc_clangd", "file": "D:/Desktop/100/chat-forge-master/Client/build/Desktop_Qt_6_9_1_MSVC2022_64bit-Debug/ui_addfriend.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-nologo", "-Zc:wchar_t", "-FS", "-Zc:rvalueCast", "-Zc:inline", "-Zc:strictStrings", "-Zc:<PERSON><PERSON><PERSON>", "-permissive-", "-Zc:__cplusplus", "-Zc:externConstexpr", "-<PERSON>i", "-MDd", "-clang:-std=c++17", "-utf-8", "-W3", "-w34100", "-w34189", "-w44456", "-w44457", "-w44458", "-wd4577", "-wd4467", "-EHsc", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "-fms-compatibility-version=19.44", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-DWIN64", "-DRC_ICONS", "-DQT_DEPRECATED_WARNINGS", "-DQT_QML_DEBUG", "-DQT_OPENGLWIDGETS_LIB", "-DQT_OPENGL_LIB", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IE:\\YingYong\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IE:\\YingYong\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\Desktop\\100\\chat-forge-master\\Client", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\Adding", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\Chatting", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\IconSetting", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\Tools", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\Rewriting", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtOpenGLWidgets", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtOpenGL", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtWidgets", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtGui", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtNetwork", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtCore", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\build\\Desktop_Qt_6_9_1_MSVC2022_64bit-Debug\\debug", "-ID:\\Desktop\\100\\chat-forge-master\\Client\\build\\Desktop_Qt_6_9_1_MSVC2022_64bit-Debug", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:E:\\YingYong\\QT\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Tools\\MSVC\\14.44.35207\\ATLMFC\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\um", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\shared", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\winrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\cppwinrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\NETFXSDK\\4.8\\include\\um", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/TP", "D:\\Desktop\\100\\chat-forge-master\\Client\\build\\Desktop_Qt_6_9_1_MSVC2022_64bit-Debug\\ui_logging.h"], "directory": "D:/Desktop/100/chat-forge-master/Client/build/Desktop_Qt_6_9_1_MSVC2022_64bit-Debug/.qtc_clangd", "file": "D:/Desktop/100/chat-forge-master/Client/build/Desktop_Qt_6_9_1_MSVC2022_64bit-Debug/ui_logging.h"}]