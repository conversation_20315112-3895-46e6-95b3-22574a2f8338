# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.20

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/local/bin/cmake

# The command to remove a file.
RM = /usr/local/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/OurChat/server

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/OurChat/server/build

#=============================================================================
# Directory level rules for the build root directory

# The main recursive "all" target.
all: CMakeFiles/server.dir/all
all: thirdparty/SQLiteCpp/all
all: thirdparty/json-develop/all
.PHONY : all

# The main recursive "preinstall" target.
preinstall: thirdparty/SQLiteCpp/preinstall
preinstall: thirdparty/json-develop/preinstall
.PHONY : preinstall

# The main recursive "clean" target.
clean: CMakeFiles/server.dir/clean
clean: thirdparty/SQLiteCpp/clean
clean: thirdparty/json-develop/clean
.PHONY : clean

#=============================================================================
# Directory level rules for directory thirdparty/SQLiteCpp

# Recursive "all" directory target.
thirdparty/SQLiteCpp/all: thirdparty/SQLiteCpp/CMakeFiles/SQLiteCpp_cpplint.dir/all
thirdparty/SQLiteCpp/all: thirdparty/SQLiteCpp/CMakeFiles/SQLiteCpp.dir/all
thirdparty/SQLiteCpp/all: thirdparty/SQLiteCpp/sqlite3/all
.PHONY : thirdparty/SQLiteCpp/all

# Recursive "preinstall" directory target.
thirdparty/SQLiteCpp/preinstall: thirdparty/SQLiteCpp/sqlite3/preinstall
.PHONY : thirdparty/SQLiteCpp/preinstall

# Recursive "clean" directory target.
thirdparty/SQLiteCpp/clean: thirdparty/SQLiteCpp/CMakeFiles/SQLiteCpp_cpplint.dir/clean
thirdparty/SQLiteCpp/clean: thirdparty/SQLiteCpp/CMakeFiles/SQLiteCpp.dir/clean
thirdparty/SQLiteCpp/clean: thirdparty/SQLiteCpp/sqlite3/clean
.PHONY : thirdparty/SQLiteCpp/clean

#=============================================================================
# Directory level rules for directory thirdparty/SQLiteCpp/sqlite3

# Recursive "all" directory target.
thirdparty/SQLiteCpp/sqlite3/all: thirdparty/SQLiteCpp/sqlite3/CMakeFiles/sqlite3.dir/all
.PHONY : thirdparty/SQLiteCpp/sqlite3/all

# Recursive "preinstall" directory target.
thirdparty/SQLiteCpp/sqlite3/preinstall:
.PHONY : thirdparty/SQLiteCpp/sqlite3/preinstall

# Recursive "clean" directory target.
thirdparty/SQLiteCpp/sqlite3/clean: thirdparty/SQLiteCpp/sqlite3/CMakeFiles/sqlite3.dir/clean
.PHONY : thirdparty/SQLiteCpp/sqlite3/clean

#=============================================================================
# Directory level rules for directory thirdparty/json-develop

# Recursive "all" directory target.
thirdparty/json-develop/all:
.PHONY : thirdparty/json-develop/all

# Recursive "preinstall" directory target.
thirdparty/json-develop/preinstall:
.PHONY : thirdparty/json-develop/preinstall

# Recursive "clean" directory target.
thirdparty/json-develop/clean:
.PHONY : thirdparty/json-develop/clean

#=============================================================================
# Target rules for target CMakeFiles/server.dir

# All Build rule for target.
CMakeFiles/server.dir/all: thirdparty/SQLiteCpp/CMakeFiles/SQLiteCpp.dir/all
CMakeFiles/server.dir/all: thirdparty/SQLiteCpp/sqlite3/CMakeFiles/sqlite3.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/server.dir/build.make CMakeFiles/server.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/server.dir/build.make CMakeFiles/server.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/OurChat/server/build/CMakeFiles --progress-num=9,10,11,12,13,14 "Built target server"
.PHONY : CMakeFiles/server.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/server.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/OurChat/server/build/CMakeFiles 16
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/server.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/OurChat/server/build/CMakeFiles 0
.PHONY : CMakeFiles/server.dir/rule

# Convenience name for target.
server: CMakeFiles/server.dir/rule
.PHONY : server

# clean rule for target.
CMakeFiles/server.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/server.dir/build.make CMakeFiles/server.dir/clean
.PHONY : CMakeFiles/server.dir/clean

#=============================================================================
# Target rules for target thirdparty/SQLiteCpp/CMakeFiles/SQLiteCpp_cpplint.dir

# All Build rule for target.
thirdparty/SQLiteCpp/CMakeFiles/SQLiteCpp_cpplint.dir/all:
	$(MAKE) $(MAKESILENT) -f thirdparty/SQLiteCpp/CMakeFiles/SQLiteCpp_cpplint.dir/build.make thirdparty/SQLiteCpp/CMakeFiles/SQLiteCpp_cpplint.dir/depend
	$(MAKE) $(MAKESILENT) -f thirdparty/SQLiteCpp/CMakeFiles/SQLiteCpp_cpplint.dir/build.make thirdparty/SQLiteCpp/CMakeFiles/SQLiteCpp_cpplint.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/OurChat/server/build/CMakeFiles --progress-num= "Built target SQLiteCpp_cpplint"
.PHONY : thirdparty/SQLiteCpp/CMakeFiles/SQLiteCpp_cpplint.dir/all

# Build rule for subdir invocation for target.
thirdparty/SQLiteCpp/CMakeFiles/SQLiteCpp_cpplint.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/OurChat/server/build/CMakeFiles 0
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 thirdparty/SQLiteCpp/CMakeFiles/SQLiteCpp_cpplint.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/OurChat/server/build/CMakeFiles 0
.PHONY : thirdparty/SQLiteCpp/CMakeFiles/SQLiteCpp_cpplint.dir/rule

# Convenience name for target.
SQLiteCpp_cpplint: thirdparty/SQLiteCpp/CMakeFiles/SQLiteCpp_cpplint.dir/rule
.PHONY : SQLiteCpp_cpplint

# clean rule for target.
thirdparty/SQLiteCpp/CMakeFiles/SQLiteCpp_cpplint.dir/clean:
	$(MAKE) $(MAKESILENT) -f thirdparty/SQLiteCpp/CMakeFiles/SQLiteCpp_cpplint.dir/build.make thirdparty/SQLiteCpp/CMakeFiles/SQLiteCpp_cpplint.dir/clean
.PHONY : thirdparty/SQLiteCpp/CMakeFiles/SQLiteCpp_cpplint.dir/clean

#=============================================================================
# Target rules for target thirdparty/SQLiteCpp/CMakeFiles/SQLiteCpp.dir

# All Build rule for target.
thirdparty/SQLiteCpp/CMakeFiles/SQLiteCpp.dir/all: thirdparty/SQLiteCpp/sqlite3/CMakeFiles/sqlite3.dir/all
	$(MAKE) $(MAKESILENT) -f thirdparty/SQLiteCpp/CMakeFiles/SQLiteCpp.dir/build.make thirdparty/SQLiteCpp/CMakeFiles/SQLiteCpp.dir/depend
	$(MAKE) $(MAKESILENT) -f thirdparty/SQLiteCpp/CMakeFiles/SQLiteCpp.dir/build.make thirdparty/SQLiteCpp/CMakeFiles/SQLiteCpp.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/OurChat/server/build/CMakeFiles --progress-num=1,2,3,4,5,6,7,8 "Built target SQLiteCpp"
.PHONY : thirdparty/SQLiteCpp/CMakeFiles/SQLiteCpp.dir/all

# Build rule for subdir invocation for target.
thirdparty/SQLiteCpp/CMakeFiles/SQLiteCpp.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/OurChat/server/build/CMakeFiles 10
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 thirdparty/SQLiteCpp/CMakeFiles/SQLiteCpp.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/OurChat/server/build/CMakeFiles 0
.PHONY : thirdparty/SQLiteCpp/CMakeFiles/SQLiteCpp.dir/rule

# Convenience name for target.
SQLiteCpp: thirdparty/SQLiteCpp/CMakeFiles/SQLiteCpp.dir/rule
.PHONY : SQLiteCpp

# clean rule for target.
thirdparty/SQLiteCpp/CMakeFiles/SQLiteCpp.dir/clean:
	$(MAKE) $(MAKESILENT) -f thirdparty/SQLiteCpp/CMakeFiles/SQLiteCpp.dir/build.make thirdparty/SQLiteCpp/CMakeFiles/SQLiteCpp.dir/clean
.PHONY : thirdparty/SQLiteCpp/CMakeFiles/SQLiteCpp.dir/clean

#=============================================================================
# Target rules for target thirdparty/SQLiteCpp/sqlite3/CMakeFiles/sqlite3.dir

# All Build rule for target.
thirdparty/SQLiteCpp/sqlite3/CMakeFiles/sqlite3.dir/all:
	$(MAKE) $(MAKESILENT) -f thirdparty/SQLiteCpp/sqlite3/CMakeFiles/sqlite3.dir/build.make thirdparty/SQLiteCpp/sqlite3/CMakeFiles/sqlite3.dir/depend
	$(MAKE) $(MAKESILENT) -f thirdparty/SQLiteCpp/sqlite3/CMakeFiles/sqlite3.dir/build.make thirdparty/SQLiteCpp/sqlite3/CMakeFiles/sqlite3.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/OurChat/server/build/CMakeFiles --progress-num=15,16 "Built target sqlite3"
.PHONY : thirdparty/SQLiteCpp/sqlite3/CMakeFiles/sqlite3.dir/all

# Build rule for subdir invocation for target.
thirdparty/SQLiteCpp/sqlite3/CMakeFiles/sqlite3.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/OurChat/server/build/CMakeFiles 2
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 thirdparty/SQLiteCpp/sqlite3/CMakeFiles/sqlite3.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/OurChat/server/build/CMakeFiles 0
.PHONY : thirdparty/SQLiteCpp/sqlite3/CMakeFiles/sqlite3.dir/rule

# Convenience name for target.
sqlite3: thirdparty/SQLiteCpp/sqlite3/CMakeFiles/sqlite3.dir/rule
.PHONY : sqlite3

# clean rule for target.
thirdparty/SQLiteCpp/sqlite3/CMakeFiles/sqlite3.dir/clean:
	$(MAKE) $(MAKESILENT) -f thirdparty/SQLiteCpp/sqlite3/CMakeFiles/sqlite3.dir/build.make thirdparty/SQLiteCpp/sqlite3/CMakeFiles/sqlite3.dir/clean
.PHONY : thirdparty/SQLiteCpp/sqlite3/CMakeFiles/sqlite3.dir/clean

#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

