<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>FriendItem</class>
 <widget class="QWidget" name="FriendItem">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>250</width>
    <height>71</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <property name="styleSheet">
   <string notr="true"/>
  </property>
  <widget class="FriendIconLabel" name="label_icon">
   <property name="geometry">
    <rect>
     <x>5</x>
     <y>6</y>
     <width>61</width>
     <height>61</height>
    </rect>
   </property>
   <property name="mouseTracking">
    <bool>true</bool>
   </property>
   <property name="styleSheet">
    <string notr="true"/>
   </property>
   <property name="text">
    <string/>
   </property>
   <property name="textFormat">
    <enum>Qt::RichText</enum>
   </property>
   <property name="pixmap">
    <pixmap>:/pic/src/QQIcon/icon2.png</pixmap>
   </property>
   <property name="scaledContents">
    <bool>true</bool>
   </property>
  </widget>
  <widget class="QLabel" name="label_name">
   <property name="geometry">
    <rect>
     <x>79</x>
     <y>10</y>
     <width>161</width>
     <height>21</height>
    </rect>
   </property>
   <property name="styleSheet">
    <string notr="true">background-color: transparent</string>
   </property>
   <property name="text">
    <string>名字</string>
   </property>
  </widget>
  <widget class="QLabel" name="label_sig">
   <property name="geometry">
    <rect>
     <x>79</x>
     <y>40</y>
     <width>161</width>
     <height>21</height>
    </rect>
   </property>
   <property name="styleSheet">
    <string notr="true">background-color: transparent</string>
   </property>
   <property name="text">
    <string>个性签名</string>
   </property>
  </widget>
  <widget class="QLineEdit" name="lineEdit_newMsg">
   <property name="geometry">
    <rect>
     <x>50</x>
     <y>2</y>
     <width>20</width>
     <height>20</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <family>Bahnschrift SemiLight</family>
    </font>
   </property>
   <property name="cursor">
    <cursorShape>BlankCursor</cursorShape>
   </property>
   <property name="mouseTracking">
    <bool>false</bool>
   </property>
   <property name="styleSheet">
    <string notr="true">min-width: 20px; min-height: 20px;max-width:20px; max-height: 20px;border-radius: 10px; ;background: #E13D4E;
color: #ffffff</string>
   </property>
   <property name="text">
    <string>0</string>
   </property>
   <property name="maxLength">
    <number>10</number>
   </property>
   <property name="alignment">
    <set>Qt::AlignCenter</set>
   </property>
   <property name="readOnly">
    <bool>true</bool>
   </property>
  </widget>
 </widget>
 <customwidgets>
  <customwidget>
   <class>FriendIconLabel</class>
   <extends>QLabel</extends>
   <header location="global">friendiconlabel.h</header>
  </customwidget>
 </customwidgets>
 <resources/>
 <connections/>
</ui>
