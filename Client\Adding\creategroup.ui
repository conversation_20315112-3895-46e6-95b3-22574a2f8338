<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>CreateGroup</class>
 <widget class="QDialog" name="CreateGroup">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>400</width>
    <height>300</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>创建群聊</string>
  </property>
  <property name="styleSheet">
   <string notr="true">QDialog {
    background-color: rgb(255, 255, 255);
    border-radius: 10px;
}

QLabel {
    color: rgb(51, 51, 51);
    font-size: 14px;
    font-weight: bold;
}

QLineEdit, QTextEdit {
    border: 2px solid rgb(200, 200, 200);
    border-radius: 5px;
    padding: 8px;
    font-size: 12px;
    background-color: rgb(250, 250, 250);
}

QLineEdit:focus, QTextEdit:focus {
    border-color: rgb(64, 158, 255);
}

QPushButton {
    background-color: rgb(64, 158, 255);
    color: white;
    border: none;
    border-radius: 5px;
    padding: 8px 16px;
    font-size: 12px;
    font-weight: bold;
}

QPushButton:hover {
    background-color: rgb(100, 180, 255);
}

QPushButton:pressed {
    background-color: rgb(40, 140, 230);
}

QPushButton#pushButton_cancel {
    background-color: rgb(150, 150, 150);
}

QPushButton#pushButton_cancel:hover {
    background-color: rgb(170, 170, 170);
}</string>
  </property>
  <widget class="QLabel" name="label_title">
   <property name="geometry">
    <rect>
     <x>20</x>
     <y>20</y>
     <width>360</width>
     <height>30</height>
    </rect>
   </property>
   <property name="text">
    <string>创建群聊</string>
   </property>
   <property name="alignment">
    <set>Qt::AlignCenter</set>
   </property>
   <property name="styleSheet">
    <string notr="true">font-size: 18px;</string>
   </property>
  </widget>
  <widget class="QLabel" name="label_groupName">
   <property name="geometry">
    <rect>
     <x>30</x>
     <y>70</y>
     <width>80</width>
     <height>25</height>
    </rect>
   </property>
   <property name="text">
    <string>群名称：</string>
   </property>
  </widget>
  <widget class="QLineEdit" name="lineEdit_groupName">
   <property name="geometry">
    <rect>
     <x>120</x>
     <y>70</y>
     <width>250</width>
     <height>30</height>
    </rect>
   </property>
   <property name="maxLength">
    <number>20</number>
   </property>
  </widget>
  <widget class="QLabel" name="label_description">
   <property name="geometry">
    <rect>
     <x>30</x>
     <y>120</y>
     <width>80</width>
     <height>25</height>
    </rect>
   </property>
   <property name="text">
    <string>群描述：</string>
   </property>
  </widget>
  <widget class="QTextEdit" name="textEdit_description">
   <property name="geometry">
    <rect>
     <x>120</x>
     <y>120</y>
     <width>250</width>
     <height>80</height>
    </rect>
   </property>
  </widget>
  <widget class="QPushButton" name="pushButton_create">
   <property name="geometry">
    <rect>
     <x>120</x>
     <y>230</y>
     <width>100</width>
     <height>35</height>
    </rect>
   </property>
   <property name="text">
    <string>创建群聊</string>
   </property>
  </widget>
  <widget class="QPushButton" name="pushButton_cancel">
   <property name="geometry">
    <rect>
     <x>250</x>
     <y>230</y>
     <width>100</width>
     <height>35</height>
    </rect>
   </property>
   <property name="text">
    <string>取消</string>
   </property>
  </widget>
 </widget>
 <resources/>
 <connections/>
</ui>
