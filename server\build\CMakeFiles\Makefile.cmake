# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.20

# The generator used is:
set(CMAKE_DEPENDS_GENERATOR "Unix Makefiles")

# The top level Makefile was generated from the following files:
set(CMAKE_MAKEFILE_DEPENDS
  "CMakeCache.txt"
  "../CMakeLists.txt"
  "CMakeFiles/3.20.2/CMakeCCompiler.cmake"
  "CMakeFiles/3.20.2/CMakeCXXCompiler.cmake"
  "CMakeFiles/3.20.2/CMakeSystem.cmake"
  "../thirdparty/SQLiteCpp/CMakeLists.txt"
  "../thirdparty/SQLiteCpp/cmake/SQLiteCppConfig.cmake.in"
  "../thirdparty/SQLiteCpp/sqlite3/CMakeLists.txt"
  "../thirdparty/json-develop/CMakeLists.txt"
  "../thirdparty/json-develop/cmake/config.cmake.in"
  "../thirdparty/json-develop/cmake/nlohmann_jsonConfigVersion.cmake.in"
  "../thirdparty/json-develop/cmake/pkg-config.pc.in"
  "/usr/local/share/cmake-3.20/Modules/BasicConfigVersion-AnyNewerVersion.cmake.in"
  "/usr/local/share/cmake-3.20/Modules/CMakeCCompiler.cmake.in"
  "/usr/local/share/cmake-3.20/Modules/CMakeCCompilerABI.c"
  "/usr/local/share/cmake-3.20/Modules/CMakeCInformation.cmake"
  "/usr/local/share/cmake-3.20/Modules/CMakeCXXCompiler.cmake.in"
  "/usr/local/share/cmake-3.20/Modules/CMakeCXXCompilerABI.cpp"
  "/usr/local/share/cmake-3.20/Modules/CMakeCXXInformation.cmake"
  "/usr/local/share/cmake-3.20/Modules/CMakeCommonLanguageInclude.cmake"
  "/usr/local/share/cmake-3.20/Modules/CMakeCompilerIdDetection.cmake"
  "/usr/local/share/cmake-3.20/Modules/CMakeDetermineCCompiler.cmake"
  "/usr/local/share/cmake-3.20/Modules/CMakeDetermineCXXCompiler.cmake"
  "/usr/local/share/cmake-3.20/Modules/CMakeDetermineCompileFeatures.cmake"
  "/usr/local/share/cmake-3.20/Modules/CMakeDetermineCompiler.cmake"
  "/usr/local/share/cmake-3.20/Modules/CMakeDetermineCompilerABI.cmake"
  "/usr/local/share/cmake-3.20/Modules/CMakeDetermineCompilerId.cmake"
  "/usr/local/share/cmake-3.20/Modules/CMakeDetermineSystem.cmake"
  "/usr/local/share/cmake-3.20/Modules/CMakeFindBinUtils.cmake"
  "/usr/local/share/cmake-3.20/Modules/CMakeGenericSystem.cmake"
  "/usr/local/share/cmake-3.20/Modules/CMakeInitializeConfigs.cmake"
  "/usr/local/share/cmake-3.20/Modules/CMakeLanguageInformation.cmake"
  "/usr/local/share/cmake-3.20/Modules/CMakePackageConfigHelpers.cmake"
  "/usr/local/share/cmake-3.20/Modules/CMakeParseImplicitIncludeInfo.cmake"
  "/usr/local/share/cmake-3.20/Modules/CMakeParseImplicitLinkInfo.cmake"
  "/usr/local/share/cmake-3.20/Modules/CMakeParseLibraryArchitecture.cmake"
  "/usr/local/share/cmake-3.20/Modules/CMakeSystem.cmake.in"
  "/usr/local/share/cmake-3.20/Modules/CMakeSystemSpecificInformation.cmake"
  "/usr/local/share/cmake-3.20/Modules/CMakeSystemSpecificInitialize.cmake"
  "/usr/local/share/cmake-3.20/Modules/CMakeTestCCompiler.cmake"
  "/usr/local/share/cmake-3.20/Modules/CMakeTestCXXCompiler.cmake"
  "/usr/local/share/cmake-3.20/Modules/CMakeTestCompilerCommon.cmake"
  "/usr/local/share/cmake-3.20/Modules/CMakeUnixFindMake.cmake"
  "/usr/local/share/cmake-3.20/Modules/CheckCSourceCompiles.cmake"
  "/usr/local/share/cmake-3.20/Modules/CheckForPthreads.c"
  "/usr/local/share/cmake-3.20/Modules/CheckIncludeFile.c.in"
  "/usr/local/share/cmake-3.20/Modules/CheckIncludeFile.cmake"
  "/usr/local/share/cmake-3.20/Modules/CheckLibraryExists.cmake"
  "/usr/local/share/cmake-3.20/Modules/Compiler/ADSP-DetermineCompiler.cmake"
  "/usr/local/share/cmake-3.20/Modules/Compiler/ARMCC-DetermineCompiler.cmake"
  "/usr/local/share/cmake-3.20/Modules/Compiler/ARMClang-DetermineCompiler.cmake"
  "/usr/local/share/cmake-3.20/Modules/Compiler/AppleClang-DetermineCompiler.cmake"
  "/usr/local/share/cmake-3.20/Modules/Compiler/Borland-DetermineCompiler.cmake"
  "/usr/local/share/cmake-3.20/Modules/Compiler/Bruce-C-DetermineCompiler.cmake"
  "/usr/local/share/cmake-3.20/Modules/Compiler/CMakeCommonCompilerMacros.cmake"
  "/usr/local/share/cmake-3.20/Modules/Compiler/Clang-DetermineCompiler.cmake"
  "/usr/local/share/cmake-3.20/Modules/Compiler/Clang-DetermineCompilerInternal.cmake"
  "/usr/local/share/cmake-3.20/Modules/Compiler/Comeau-CXX-DetermineCompiler.cmake"
  "/usr/local/share/cmake-3.20/Modules/Compiler/Compaq-C-DetermineCompiler.cmake"
  "/usr/local/share/cmake-3.20/Modules/Compiler/Compaq-CXX-DetermineCompiler.cmake"
  "/usr/local/share/cmake-3.20/Modules/Compiler/Cray-DetermineCompiler.cmake"
  "/usr/local/share/cmake-3.20/Modules/Compiler/Embarcadero-DetermineCompiler.cmake"
  "/usr/local/share/cmake-3.20/Modules/Compiler/Fujitsu-DetermineCompiler.cmake"
  "/usr/local/share/cmake-3.20/Modules/Compiler/GHS-DetermineCompiler.cmake"
  "/usr/local/share/cmake-3.20/Modules/Compiler/GNU-C-DetermineCompiler.cmake"
  "/usr/local/share/cmake-3.20/Modules/Compiler/GNU-C.cmake"
  "/usr/local/share/cmake-3.20/Modules/Compiler/GNU-CXX-DetermineCompiler.cmake"
  "/usr/local/share/cmake-3.20/Modules/Compiler/GNU-CXX.cmake"
  "/usr/local/share/cmake-3.20/Modules/Compiler/GNU-FindBinUtils.cmake"
  "/usr/local/share/cmake-3.20/Modules/Compiler/GNU.cmake"
  "/usr/local/share/cmake-3.20/Modules/Compiler/HP-C-DetermineCompiler.cmake"
  "/usr/local/share/cmake-3.20/Modules/Compiler/HP-CXX-DetermineCompiler.cmake"
  "/usr/local/share/cmake-3.20/Modules/Compiler/IAR-DetermineCompiler.cmake"
  "/usr/local/share/cmake-3.20/Modules/Compiler/IBMCPP-C-DetermineVersionInternal.cmake"
  "/usr/local/share/cmake-3.20/Modules/Compiler/IBMCPP-CXX-DetermineVersionInternal.cmake"
  "/usr/local/share/cmake-3.20/Modules/Compiler/Intel-DetermineCompiler.cmake"
  "/usr/local/share/cmake-3.20/Modules/Compiler/IntelLLVM-DetermineCompiler.cmake"
  "/usr/local/share/cmake-3.20/Modules/Compiler/MSVC-DetermineCompiler.cmake"
  "/usr/local/share/cmake-3.20/Modules/Compiler/NVHPC-DetermineCompiler.cmake"
  "/usr/local/share/cmake-3.20/Modules/Compiler/NVIDIA-DetermineCompiler.cmake"
  "/usr/local/share/cmake-3.20/Modules/Compiler/OpenWatcom-DetermineCompiler.cmake"
  "/usr/local/share/cmake-3.20/Modules/Compiler/PGI-DetermineCompiler.cmake"
  "/usr/local/share/cmake-3.20/Modules/Compiler/PathScale-DetermineCompiler.cmake"
  "/usr/local/share/cmake-3.20/Modules/Compiler/SCO-DetermineCompiler.cmake"
  "/usr/local/share/cmake-3.20/Modules/Compiler/SDCC-C-DetermineCompiler.cmake"
  "/usr/local/share/cmake-3.20/Modules/Compiler/SunPro-C-DetermineCompiler.cmake"
  "/usr/local/share/cmake-3.20/Modules/Compiler/SunPro-CXX-DetermineCompiler.cmake"
  "/usr/local/share/cmake-3.20/Modules/Compiler/TI-DetermineCompiler.cmake"
  "/usr/local/share/cmake-3.20/Modules/Compiler/TinyCC-C-DetermineCompiler.cmake"
  "/usr/local/share/cmake-3.20/Modules/Compiler/VisualAge-C-DetermineCompiler.cmake"
  "/usr/local/share/cmake-3.20/Modules/Compiler/VisualAge-CXX-DetermineCompiler.cmake"
  "/usr/local/share/cmake-3.20/Modules/Compiler/Watcom-DetermineCompiler.cmake"
  "/usr/local/share/cmake-3.20/Modules/Compiler/XL-C-DetermineCompiler.cmake"
  "/usr/local/share/cmake-3.20/Modules/Compiler/XL-CXX-DetermineCompiler.cmake"
  "/usr/local/share/cmake-3.20/Modules/Compiler/XLClang-C-DetermineCompiler.cmake"
  "/usr/local/share/cmake-3.20/Modules/Compiler/XLClang-CXX-DetermineCompiler.cmake"
  "/usr/local/share/cmake-3.20/Modules/Compiler/zOS-C-DetermineCompiler.cmake"
  "/usr/local/share/cmake-3.20/Modules/Compiler/zOS-CXX-DetermineCompiler.cmake"
  "/usr/local/share/cmake-3.20/Modules/ExternalProject.cmake"
  "/usr/local/share/cmake-3.20/Modules/FindPackageHandleStandardArgs.cmake"
  "/usr/local/share/cmake-3.20/Modules/FindPackageMessage.cmake"
  "/usr/local/share/cmake-3.20/Modules/FindPythonInterp.cmake"
  "/usr/local/share/cmake-3.20/Modules/FindThreads.cmake"
  "/usr/local/share/cmake-3.20/Modules/GNUInstallDirs.cmake"
  "/usr/local/share/cmake-3.20/Modules/Internal/CheckSourceCompiles.cmake"
  "/usr/local/share/cmake-3.20/Modules/Internal/FeatureTesting.cmake"
  "/usr/local/share/cmake-3.20/Modules/Platform/Linux-Determine-CXX.cmake"
  "/usr/local/share/cmake-3.20/Modules/Platform/Linux-GNU-C.cmake"
  "/usr/local/share/cmake-3.20/Modules/Platform/Linux-GNU-CXX.cmake"
  "/usr/local/share/cmake-3.20/Modules/Platform/Linux-GNU.cmake"
  "/usr/local/share/cmake-3.20/Modules/Platform/Linux.cmake"
  "/usr/local/share/cmake-3.20/Modules/Platform/UnixPaths.cmake"
  "/usr/local/share/cmake-3.20/Modules/WriteBasicConfigVersionFile.cmake"
  )

# The corresponding makefile is:
set(CMAKE_MAKEFILE_OUTPUTS
  "Makefile"
  "CMakeFiles/cmake.check_cache"
  )

# Byproducts of CMake generate step:
set(CMAKE_MAKEFILE_PRODUCTS
  "CMakeFiles/3.20.2/CMakeSystem.cmake"
  "CMakeFiles/3.20.2/CMakeCCompiler.cmake"
  "CMakeFiles/3.20.2/CMakeCXXCompiler.cmake"
  "CMakeFiles/3.20.2/CMakeCCompiler.cmake"
  "CMakeFiles/3.20.2/CMakeCXXCompiler.cmake"
  "CMakeFiles/CMakeDirectoryInformation.cmake"
  "thirdparty/SQLiteCpp/cmake/SQLiteCppConfigVersion.cmake"
  "thirdparty/SQLiteCpp/cmake/SQLiteCppConfig.cmake"
  "thirdparty/SQLiteCpp/CMakeFiles/CMakeDirectoryInformation.cmake"
  "thirdparty/SQLiteCpp/sqlite3/CMakeFiles/CMakeDirectoryInformation.cmake"
  "thirdparty/json-develop/nlohmann_json.pc"
  "thirdparty/json-develop/nlohmann_jsonConfigVersion.cmake"
  "thirdparty/json-develop/nlohmann_jsonConfig.cmake"
  "thirdparty/json-develop/CMakeFiles/CMakeDirectoryInformation.cmake"
  )

# Dependency information for all targets:
set(CMAKE_DEPEND_INFO_FILES
  "CMakeFiles/server.dir/DependInfo.cmake"
  "thirdparty/SQLiteCpp/CMakeFiles/SQLiteCpp_cpplint.dir/DependInfo.cmake"
  "thirdparty/SQLiteCpp/CMakeFiles/SQLiteCpp.dir/DependInfo.cmake"
  "thirdparty/SQLiteCpp/sqlite3/CMakeFiles/sqlite3.dir/DependInfo.cmake"
  )
