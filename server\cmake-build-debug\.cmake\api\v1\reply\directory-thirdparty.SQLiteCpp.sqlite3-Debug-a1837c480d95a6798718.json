{"backtraceGraph": {"commands": ["install"], "files": ["thirdparty/sQLitecpp/sqlite3/CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 60, "parent": 0}, {"command": 0, "file": 0, "line": 65, "parent": 0}]}, "installers": [{"backtrace": 1, "component": "libraries", "destination": "lib", "paths": ["thirdparty/SQLiteCpp/sqlite3/libsqlite3.a"], "targetId": "sqlite3::@741eb9df4aa093c9dadc", "targetIndex": 3, "type": "target"}, {"backtrace": 2, "component": "headers", "destination": "include", "paths": ["thirdparty/sQLitecpp/sqlite3/sqlite3.h"], "type": "file"}], "paths": {"build": "thirdparty/SQLiteCpp/sqlite3", "source": "thirdparty/sQLitecpp/sqlite3"}}