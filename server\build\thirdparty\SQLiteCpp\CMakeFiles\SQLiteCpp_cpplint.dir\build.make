# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.20

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/local/bin/cmake

# The command to remove a file.
RM = /usr/local/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/OurChat/server

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/OurChat/server/build

# Utility rule file for SQLiteCpp_cpplint.

# Include any custom commands dependencies for this target.
include thirdparty/SQLiteCpp/CMakeFiles/SQLiteCpp_cpplint.dir/compiler_depend.make

# Include the progress variables for this target.
include thirdparty/SQLiteCpp/CMakeFiles/SQLiteCpp_cpplint.dir/progress.make

thirdparty/SQLiteCpp/CMakeFiles/SQLiteCpp_cpplint:
	cd /home/<USER>/OurChat/server/build/thirdparty/SQLiteCpp && /usr/bin/python /home/<USER>/OurChat/server/thirdparty/SQLiteCpp/cpplint.py --output=eclipse --verbose=3 --linelength=120 /home/<USER>/OurChat/server/thirdparty/SQLiteCpp/src/Backup.cpp /home/<USER>/OurChat/server/thirdparty/SQLiteCpp/src/Column.cpp /home/<USER>/OurChat/server/thirdparty/SQLiteCpp/src/Database.cpp /home/<USER>/OurChat/server/thirdparty/SQLiteCpp/src/Exception.cpp /home/<USER>/OurChat/server/thirdparty/SQLiteCpp/src/Savepoint.cpp /home/<USER>/OurChat/server/thirdparty/SQLiteCpp/src/Statement.cpp /home/<USER>/OurChat/server/thirdparty/SQLiteCpp/src/Transaction.cpp /home/<USER>/OurChat/server/thirdparty/SQLiteCpp/include/SQLiteCpp/SQLiteCpp.h /home/<USER>/OurChat/server/thirdparty/SQLiteCpp/include/SQLiteCpp/Assertion.h /home/<USER>/OurChat/server/thirdparty/SQLiteCpp/include/SQLiteCpp/Backup.h /home/<USER>/OurChat/server/thirdparty/SQLiteCpp/include/SQLiteCpp/Column.h /home/<USER>/OurChat/server/thirdparty/SQLiteCpp/include/SQLiteCpp/Database.h /home/<USER>/OurChat/server/thirdparty/SQLiteCpp/include/SQLiteCpp/Exception.h /home/<USER>/OurChat/server/thirdparty/SQLiteCpp/include/SQLiteCpp/Savepoint.h /home/<USER>/OurChat/server/thirdparty/SQLiteCpp/include/SQLiteCpp/Statement.h /home/<USER>/OurChat/server/thirdparty/SQLiteCpp/include/SQLiteCpp/Transaction.h /home/<USER>/OurChat/server/thirdparty/SQLiteCpp/include/SQLiteCpp/VariadicBind.h /home/<USER>/OurChat/server/thirdparty/SQLiteCpp/include/SQLiteCpp/ExecuteMany.h

SQLiteCpp_cpplint: thirdparty/SQLiteCpp/CMakeFiles/SQLiteCpp_cpplint
SQLiteCpp_cpplint: thirdparty/SQLiteCpp/CMakeFiles/SQLiteCpp_cpplint.dir/build.make
.PHONY : SQLiteCpp_cpplint

# Rule to build all files generated by this target.
thirdparty/SQLiteCpp/CMakeFiles/SQLiteCpp_cpplint.dir/build: SQLiteCpp_cpplint
.PHONY : thirdparty/SQLiteCpp/CMakeFiles/SQLiteCpp_cpplint.dir/build

thirdparty/SQLiteCpp/CMakeFiles/SQLiteCpp_cpplint.dir/clean:
	cd /home/<USER>/OurChat/server/build/thirdparty/SQLiteCpp && $(CMAKE_COMMAND) -P CMakeFiles/SQLiteCpp_cpplint.dir/cmake_clean.cmake
.PHONY : thirdparty/SQLiteCpp/CMakeFiles/SQLiteCpp_cpplint.dir/clean

thirdparty/SQLiteCpp/CMakeFiles/SQLiteCpp_cpplint.dir/depend:
	cd /home/<USER>/OurChat/server/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/OurChat/server /home/<USER>/OurChat/server/thirdparty/SQLiteCpp /home/<USER>/OurChat/server/build /home/<USER>/OurChat/server/build/thirdparty/SQLiteCpp /home/<USER>/OurChat/server/build/thirdparty/SQLiteCpp/CMakeFiles/SQLiteCpp_cpplint.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : thirdparty/SQLiteCpp/CMakeFiles/SQLiteCpp_cpplint.dir/depend

