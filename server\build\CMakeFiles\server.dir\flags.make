# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.20

# compile CXX with /opt/rh/devtoolset-8/root/usr/bin/c++
CXX_DEFINES = -DSQLITE_ENABLE_COLUMN_METADATA

CXX_INCLUDES = -I/home/<USER>/OurChat/server/thirdparty/SQLiteCpp/include -I/home/<USER>/OurChat/server/thirdparty/SQLiteCpp/sqlite3 -I/home/<USER>/OurChat/server/thirdparty/json-develop/include

CXX_FLAGS = -pthread -std=gnu++17

