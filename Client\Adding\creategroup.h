#ifndef CREATEGROUP_H
#define CREATEGROUP_H

#include <QDialog>
#include <QJsonObject>
#include <QJsonDocument>
#include <QMessageBox>
#include <QGraphicsDropShadowEffect>
#include "Tools/tcpclient.h"

QT_BEGIN_NAMESPACE
namespace Ui { class CreateGroup; }
QT_END_NAMESPACE

using json = QJsonObject;

class CreateGroup : public QDialog
{
    Q_OBJECT

public:
    CreateGroup(SelfInfo info, TcpClient *t, QWidget *parent = nullptr);
    ~CreateGroup();

private slots:
    void on_pushButton_create_clicked();
    void on_pushButton_cancel_clicked();

public slots:
    void CmdHandler(json msg);

private:
    void Init();

private:
    Ui::CreateGroup *ui;
    SelfInfo info;
    TcpClient *t;
    QMessageBox *msgBox;
};

#endif // CREATEGROUP_H
