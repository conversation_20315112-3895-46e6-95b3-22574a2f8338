# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.20

# compile CXX with /opt/rh/devtoolset-8/root/usr/bin/c++
CXX_DEFINES = -DSQLITE_ENABLE_COLUMN_METADATA

CXX_INCLUDES = -I/home/<USER>/OurChat/server/thirdparty/SQLiteCpp/include -I/home/<USER>/OurChat/server/thirdparty/SQLiteCpp/sqlite3

CXX_FLAGS =  -Wall -Wextra -Wpedantic -Wswitch-enum -Wshadow -Wno-long-long -fPIC -fstack-protector -pthread -std=gnu++17

