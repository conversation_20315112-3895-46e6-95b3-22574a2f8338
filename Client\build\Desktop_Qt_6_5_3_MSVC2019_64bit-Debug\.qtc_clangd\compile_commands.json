[{"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-nologo", "-Zc:wchar_t", "-FS", "-Zc:rvalueCast", "-Zc:inline", "-Zc:strictStrings", "-Zc:<PERSON><PERSON><PERSON>", "-permissive-", "-Zc:__cplusplus", "-Zc:externConstexpr", "-<PERSON>i", "-MDd", "-clang:-std=c++17", "-utf-8", "-W3", "-w34100", "-w34189", "-w44996", "-w44456", "-w44457", "-w44458", "-wd4577", "-wd4467", "-EHsc", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "-fms-compatibility-version=19.29", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-DWIN64", "-DRC_ICONS", "-DQT_DEPRECATED_WARNINGS", "-DQT_QML_DEBUG", "-DQT_OPENGLWIDGETS_LIB", "-DQT_OPENGL_LIB", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IG:\\software\\Qt\\Qt6.5.3\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IG:\\software\\Qt\\Qt6.5.3\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IG:\\code\\project\\OurChat2\\Client", "-IG:\\code\\project\\OurChat2\\Client\\Adding", "-IG:\\code\\project\\OurChat2\\Client\\Chatting", "-IG:\\code\\project\\OurChat2\\Client\\IconSetting", "-IG:\\code\\project\\OurChat2\\Client\\Tools", "-IG:\\code\\project\\OurChat2\\Client\\Rewriting", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtOpenGLWidgets", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtOpenGL", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtWidgets", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtGui", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtNetwork", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtCore", "-IG:\\code\\project\\OurChat\\Client\\build\\Desktop_Qt_6_5_3_MSVC2019_64bit-Debug\\debug", "-IG:\\code\\project\\OurChat\\Client\\build\\Desktop_Qt_6_5_3_MSVC2019_64bit-Debug", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:G:\\software\\Qt\\Qt6.5.3\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\17\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\Community\\VC\\Tools\\MSVC\\14.29.30133\\ATLMFC\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\Community\\VC\\Tools\\MSVC\\14.29.30133\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\NETFXSDK\\4.8\\include\\um", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\ucrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\shared", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\um", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\winrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\cppwinrt", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/TP", "G:\\code\\project\\OurChat2\\Client\\Adding\\addfriend.cpp"], "directory": "G:/code/project/OurChat/Client/build/Desktop_Qt_6_5_3_MSVC2019_64bit-Debug/.qtc_clangd", "file": "G:/code/project/OurChat2/Client/Adding/addfriend.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-nologo", "-Zc:wchar_t", "-FS", "-Zc:rvalueCast", "-Zc:inline", "-Zc:strictStrings", "-Zc:<PERSON><PERSON><PERSON>", "-permissive-", "-Zc:__cplusplus", "-Zc:externConstexpr", "-<PERSON>i", "-MDd", "-clang:-std=c++17", "-utf-8", "-W3", "-w34100", "-w34189", "-w44996", "-w44456", "-w44457", "-w44458", "-wd4577", "-wd4467", "-EHsc", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "-fms-compatibility-version=19.29", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-DWIN64", "-DRC_ICONS", "-DQT_DEPRECATED_WARNINGS", "-DQT_QML_DEBUG", "-DQT_OPENGLWIDGETS_LIB", "-DQT_OPENGL_LIB", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IG:\\software\\Qt\\Qt6.5.3\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IG:\\software\\Qt\\Qt6.5.3\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IG:\\code\\project\\OurChat2\\Client", "-IG:\\code\\project\\OurChat2\\Client\\Adding", "-IG:\\code\\project\\OurChat2\\Client\\Chatting", "-IG:\\code\\project\\OurChat2\\Client\\IconSetting", "-IG:\\code\\project\\OurChat2\\Client\\Tools", "-IG:\\code\\project\\OurChat2\\Client\\Rewriting", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtOpenGLWidgets", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtOpenGL", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtWidgets", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtGui", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtNetwork", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtCore", "-IG:\\code\\project\\OurChat\\Client\\build\\Desktop_Qt_6_5_3_MSVC2019_64bit-Debug\\debug", "-IG:\\code\\project\\OurChat\\Client\\build\\Desktop_Qt_6_5_3_MSVC2019_64bit-Debug", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:G:\\software\\Qt\\Qt6.5.3\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\17\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\Community\\VC\\Tools\\MSVC\\14.29.30133\\ATLMFC\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\Community\\VC\\Tools\\MSVC\\14.29.30133\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\NETFXSDK\\4.8\\include\\um", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\ucrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\shared", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\um", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\winrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\cppwinrt", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/TP", "G:\\code\\project\\OurChat2\\Client\\Adding\\systemmessage.cpp"], "directory": "G:/code/project/OurChat/Client/build/Desktop_Qt_6_5_3_MSVC2019_64bit-Debug/.qtc_clangd", "file": "G:/code/project/OurChat2/Client/Adding/systemmessage.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-nologo", "-Zc:wchar_t", "-FS", "-Zc:rvalueCast", "-Zc:inline", "-Zc:strictStrings", "-Zc:<PERSON><PERSON><PERSON>", "-permissive-", "-Zc:__cplusplus", "-Zc:externConstexpr", "-<PERSON>i", "-MDd", "-clang:-std=c++17", "-utf-8", "-W3", "-w34100", "-w34189", "-w44996", "-w44456", "-w44457", "-w44458", "-wd4577", "-wd4467", "-EHsc", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "-fms-compatibility-version=19.29", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-DWIN64", "-DRC_ICONS", "-DQT_DEPRECATED_WARNINGS", "-DQT_QML_DEBUG", "-DQT_OPENGLWIDGETS_LIB", "-DQT_OPENGL_LIB", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IG:\\software\\Qt\\Qt6.5.3\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IG:\\software\\Qt\\Qt6.5.3\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IG:\\code\\project\\OurChat2\\Client", "-IG:\\code\\project\\OurChat2\\Client\\Adding", "-IG:\\code\\project\\OurChat2\\Client\\Chatting", "-IG:\\code\\project\\OurChat2\\Client\\IconSetting", "-IG:\\code\\project\\OurChat2\\Client\\Tools", "-IG:\\code\\project\\OurChat2\\Client\\Rewriting", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtOpenGLWidgets", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtOpenGL", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtWidgets", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtGui", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtNetwork", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtCore", "-IG:\\code\\project\\OurChat\\Client\\build\\Desktop_Qt_6_5_3_MSVC2019_64bit-Debug\\debug", "-IG:\\code\\project\\OurChat\\Client\\build\\Desktop_Qt_6_5_3_MSVC2019_64bit-Debug", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:G:\\software\\Qt\\Qt6.5.3\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\17\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\Community\\VC\\Tools\\MSVC\\14.29.30133\\ATLMFC\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\Community\\VC\\Tools\\MSVC\\14.29.30133\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\NETFXSDK\\4.8\\include\\um", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\ucrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\shared", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\um", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\winrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\cppwinrt", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/TP", "G:\\code\\project\\OurChat2\\Client\\Adding\\verificationitem.cpp"], "directory": "G:/code/project/OurChat/Client/build/Desktop_Qt_6_5_3_MSVC2019_64bit-Debug/.qtc_clangd", "file": "G:/code/project/OurChat2/Client/Adding/verificationitem.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-nologo", "-Zc:wchar_t", "-FS", "-Zc:rvalueCast", "-Zc:inline", "-Zc:strictStrings", "-Zc:<PERSON><PERSON><PERSON>", "-permissive-", "-Zc:__cplusplus", "-Zc:externConstexpr", "-<PERSON>i", "-MDd", "-clang:-std=c++17", "-utf-8", "-W3", "-w34100", "-w34189", "-w44996", "-w44456", "-w44457", "-w44458", "-wd4577", "-wd4467", "-EHsc", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "-fms-compatibility-version=19.29", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-DWIN64", "-DRC_ICONS", "-DQT_DEPRECATED_WARNINGS", "-DQT_QML_DEBUG", "-DQT_OPENGLWIDGETS_LIB", "-DQT_OPENGL_LIB", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IG:\\software\\Qt\\Qt6.5.3\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IG:\\software\\Qt\\Qt6.5.3\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IG:\\code\\project\\OurChat2\\Client", "-IG:\\code\\project\\OurChat2\\Client\\Adding", "-IG:\\code\\project\\OurChat2\\Client\\Chatting", "-IG:\\code\\project\\OurChat2\\Client\\IconSetting", "-IG:\\code\\project\\OurChat2\\Client\\Tools", "-IG:\\code\\project\\OurChat2\\Client\\Rewriting", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtOpenGLWidgets", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtOpenGL", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtWidgets", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtGui", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtNetwork", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtCore", "-IG:\\code\\project\\OurChat\\Client\\build\\Desktop_Qt_6_5_3_MSVC2019_64bit-Debug\\debug", "-IG:\\code\\project\\OurChat\\Client\\build\\Desktop_Qt_6_5_3_MSVC2019_64bit-Debug", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:G:\\software\\Qt\\Qt6.5.3\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\17\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\Community\\VC\\Tools\\MSVC\\14.29.30133\\ATLMFC\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\Community\\VC\\Tools\\MSVC\\14.29.30133\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\NETFXSDK\\4.8\\include\\um", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\ucrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\shared", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\um", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\winrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\cppwinrt", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/TP", "G:\\code\\project\\OurChat2\\Client\\Chatting\\chatlistwidget.cpp"], "directory": "G:/code/project/OurChat/Client/build/Desktop_Qt_6_5_3_MSVC2019_64bit-Debug/.qtc_clangd", "file": "G:/code/project/OurChat2/Client/Chatting/chatlistwidget.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-nologo", "-Zc:wchar_t", "-FS", "-Zc:rvalueCast", "-Zc:inline", "-Zc:strictStrings", "-Zc:<PERSON><PERSON><PERSON>", "-permissive-", "-Zc:__cplusplus", "-Zc:externConstexpr", "-<PERSON>i", "-MDd", "-clang:-std=c++17", "-utf-8", "-W3", "-w34100", "-w34189", "-w44996", "-w44456", "-w44457", "-w44458", "-wd4577", "-wd4467", "-EHsc", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "-fms-compatibility-version=19.29", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-DWIN64", "-DRC_ICONS", "-DQT_DEPRECATED_WARNINGS", "-DQT_QML_DEBUG", "-DQT_OPENGLWIDGETS_LIB", "-DQT_OPENGL_LIB", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IG:\\software\\Qt\\Qt6.5.3\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IG:\\software\\Qt\\Qt6.5.3\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IG:\\code\\project\\OurChat2\\Client", "-IG:\\code\\project\\OurChat2\\Client\\Adding", "-IG:\\code\\project\\OurChat2\\Client\\Chatting", "-IG:\\code\\project\\OurChat2\\Client\\IconSetting", "-IG:\\code\\project\\OurChat2\\Client\\Tools", "-IG:\\code\\project\\OurChat2\\Client\\Rewriting", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtOpenGLWidgets", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtOpenGL", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtWidgets", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtGui", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtNetwork", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtCore", "-IG:\\code\\project\\OurChat\\Client\\build\\Desktop_Qt_6_5_3_MSVC2019_64bit-Debug\\debug", "-IG:\\code\\project\\OurChat\\Client\\build\\Desktop_Qt_6_5_3_MSVC2019_64bit-Debug", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:G:\\software\\Qt\\Qt6.5.3\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\17\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\Community\\VC\\Tools\\MSVC\\14.29.30133\\ATLMFC\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\Community\\VC\\Tools\\MSVC\\14.29.30133\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\NETFXSDK\\4.8\\include\\um", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\ucrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\shared", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\um", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\winrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\cppwinrt", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/TP", "G:\\code\\project\\OurChat2\\Client\\Chatting\\chatwindow.cpp"], "directory": "G:/code/project/OurChat/Client/build/Desktop_Qt_6_5_3_MSVC2019_64bit-Debug/.qtc_clangd", "file": "G:/code/project/OurChat2/Client/Chatting/chatwindow.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-nologo", "-Zc:wchar_t", "-FS", "-Zc:rvalueCast", "-Zc:inline", "-Zc:strictStrings", "-Zc:<PERSON><PERSON><PERSON>", "-permissive-", "-Zc:__cplusplus", "-Zc:externConstexpr", "-<PERSON>i", "-MDd", "-clang:-std=c++17", "-utf-8", "-W3", "-w34100", "-w34189", "-w44996", "-w44456", "-w44457", "-w44458", "-wd4577", "-wd4467", "-EHsc", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "-fms-compatibility-version=19.29", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-DWIN64", "-DRC_ICONS", "-DQT_DEPRECATED_WARNINGS", "-DQT_QML_DEBUG", "-DQT_OPENGLWIDGETS_LIB", "-DQT_OPENGL_LIB", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IG:\\software\\Qt\\Qt6.5.3\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IG:\\software\\Qt\\Qt6.5.3\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IG:\\code\\project\\OurChat2\\Client", "-IG:\\code\\project\\OurChat2\\Client\\Adding", "-IG:\\code\\project\\OurChat2\\Client\\Chatting", "-IG:\\code\\project\\OurChat2\\Client\\IconSetting", "-IG:\\code\\project\\OurChat2\\Client\\Tools", "-IG:\\code\\project\\OurChat2\\Client\\Rewriting", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtOpenGLWidgets", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtOpenGL", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtWidgets", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtGui", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtNetwork", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtCore", "-IG:\\code\\project\\OurChat\\Client\\build\\Desktop_Qt_6_5_3_MSVC2019_64bit-Debug\\debug", "-IG:\\code\\project\\OurChat\\Client\\build\\Desktop_Qt_6_5_3_MSVC2019_64bit-Debug", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:G:\\software\\Qt\\Qt6.5.3\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\17\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\Community\\VC\\Tools\\MSVC\\14.29.30133\\ATLMFC\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\Community\\VC\\Tools\\MSVC\\14.29.30133\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\NETFXSDK\\4.8\\include\\um", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\ucrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\shared", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\um", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\winrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\cppwinrt", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/TP", "G:\\code\\project\\OurChat2\\Client\\Chatting\\emojiselector.cpp"], "directory": "G:/code/project/OurChat/Client/build/Desktop_Qt_6_5_3_MSVC2019_64bit-Debug/.qtc_clangd", "file": "G:/code/project/OurChat2/Client/Chatting/emojiselector.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-nologo", "-Zc:wchar_t", "-FS", "-Zc:rvalueCast", "-Zc:inline", "-Zc:strictStrings", "-Zc:<PERSON><PERSON><PERSON>", "-permissive-", "-Zc:__cplusplus", "-Zc:externConstexpr", "-<PERSON>i", "-MDd", "-clang:-std=c++17", "-utf-8", "-W3", "-w34100", "-w34189", "-w44996", "-w44456", "-w44457", "-w44458", "-wd4577", "-wd4467", "-EHsc", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "-fms-compatibility-version=19.29", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-DWIN64", "-DRC_ICONS", "-DQT_DEPRECATED_WARNINGS", "-DQT_QML_DEBUG", "-DQT_OPENGLWIDGETS_LIB", "-DQT_OPENGL_LIB", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IG:\\software\\Qt\\Qt6.5.3\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IG:\\software\\Qt\\Qt6.5.3\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IG:\\code\\project\\OurChat2\\Client", "-IG:\\code\\project\\OurChat2\\Client\\Adding", "-IG:\\code\\project\\OurChat2\\Client\\Chatting", "-IG:\\code\\project\\OurChat2\\Client\\IconSetting", "-IG:\\code\\project\\OurChat2\\Client\\Tools", "-IG:\\code\\project\\OurChat2\\Client\\Rewriting", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtOpenGLWidgets", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtOpenGL", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtWidgets", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtGui", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtNetwork", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtCore", "-IG:\\code\\project\\OurChat\\Client\\build\\Desktop_Qt_6_5_3_MSVC2019_64bit-Debug\\debug", "-IG:\\code\\project\\OurChat\\Client\\build\\Desktop_Qt_6_5_3_MSVC2019_64bit-Debug", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:G:\\software\\Qt\\Qt6.5.3\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\17\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\Community\\VC\\Tools\\MSVC\\14.29.30133\\ATLMFC\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\Community\\VC\\Tools\\MSVC\\14.29.30133\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\NETFXSDK\\4.8\\include\\um", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\ucrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\shared", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\um", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\winrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\cppwinrt", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/TP", "G:\\code\\project\\OurChat2\\Client\\IconSetting\\iconselect.cpp"], "directory": "G:/code/project/OurChat/Client/build/Desktop_Qt_6_5_3_MSVC2019_64bit-Debug/.qtc_clangd", "file": "G:/code/project/OurChat2/Client/IconSetting/iconselect.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-nologo", "-Zc:wchar_t", "-FS", "-Zc:rvalueCast", "-Zc:inline", "-Zc:strictStrings", "-Zc:<PERSON><PERSON><PERSON>", "-permissive-", "-Zc:__cplusplus", "-Zc:externConstexpr", "-<PERSON>i", "-MDd", "-clang:-std=c++17", "-utf-8", "-W3", "-w34100", "-w34189", "-w44996", "-w44456", "-w44457", "-w44458", "-wd4577", "-wd4467", "-EHsc", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "-fms-compatibility-version=19.29", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-DWIN64", "-DRC_ICONS", "-DQT_DEPRECATED_WARNINGS", "-DQT_QML_DEBUG", "-DQT_OPENGLWIDGETS_LIB", "-DQT_OPENGL_LIB", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IG:\\software\\Qt\\Qt6.5.3\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IG:\\software\\Qt\\Qt6.5.3\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IG:\\code\\project\\OurChat2\\Client", "-IG:\\code\\project\\OurChat2\\Client\\Adding", "-IG:\\code\\project\\OurChat2\\Client\\Chatting", "-IG:\\code\\project\\OurChat2\\Client\\IconSetting", "-IG:\\code\\project\\OurChat2\\Client\\Tools", "-IG:\\code\\project\\OurChat2\\Client\\Rewriting", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtOpenGLWidgets", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtOpenGL", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtWidgets", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtGui", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtNetwork", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtCore", "-IG:\\code\\project\\OurChat\\Client\\build\\Desktop_Qt_6_5_3_MSVC2019_64bit-Debug\\debug", "-IG:\\code\\project\\OurChat\\Client\\build\\Desktop_Qt_6_5_3_MSVC2019_64bit-Debug", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:G:\\software\\Qt\\Qt6.5.3\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\17\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\Community\\VC\\Tools\\MSVC\\14.29.30133\\ATLMFC\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\Community\\VC\\Tools\\MSVC\\14.29.30133\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\NETFXSDK\\4.8\\include\\um", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\ucrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\shared", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\um", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\winrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\cppwinrt", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/TP", "G:\\code\\project\\OurChat2\\Client\\Rewriting\\friendiconlabel.cpp"], "directory": "G:/code/project/OurChat/Client/build/Desktop_Qt_6_5_3_MSVC2019_64bit-Debug/.qtc_clangd", "file": "G:/code/project/OurChat2/Client/Rewriting/friendiconlabel.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-nologo", "-Zc:wchar_t", "-FS", "-Zc:rvalueCast", "-Zc:inline", "-Zc:strictStrings", "-Zc:<PERSON><PERSON><PERSON>", "-permissive-", "-Zc:__cplusplus", "-Zc:externConstexpr", "-<PERSON>i", "-MDd", "-clang:-std=c++17", "-utf-8", "-W3", "-w34100", "-w34189", "-w44996", "-w44456", "-w44457", "-w44458", "-wd4577", "-wd4467", "-EHsc", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "-fms-compatibility-version=19.29", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-DWIN64", "-DRC_ICONS", "-DQT_DEPRECATED_WARNINGS", "-DQT_QML_DEBUG", "-DQT_OPENGLWIDGETS_LIB", "-DQT_OPENGL_LIB", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IG:\\software\\Qt\\Qt6.5.3\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IG:\\software\\Qt\\Qt6.5.3\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IG:\\code\\project\\OurChat2\\Client", "-IG:\\code\\project\\OurChat2\\Client\\Adding", "-IG:\\code\\project\\OurChat2\\Client\\Chatting", "-IG:\\code\\project\\OurChat2\\Client\\IconSetting", "-IG:\\code\\project\\OurChat2\\Client\\Tools", "-IG:\\code\\project\\OurChat2\\Client\\Rewriting", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtOpenGLWidgets", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtOpenGL", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtWidgets", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtGui", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtNetwork", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtCore", "-IG:\\code\\project\\OurChat\\Client\\build\\Desktop_Qt_6_5_3_MSVC2019_64bit-Debug\\debug", "-IG:\\code\\project\\OurChat\\Client\\build\\Desktop_Qt_6_5_3_MSVC2019_64bit-Debug", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:G:\\software\\Qt\\Qt6.5.3\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\17\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\Community\\VC\\Tools\\MSVC\\14.29.30133\\ATLMFC\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\Community\\VC\\Tools\\MSVC\\14.29.30133\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\NETFXSDK\\4.8\\include\\um", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\ucrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\shared", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\um", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\winrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\cppwinrt", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/TP", "G:\\code\\project\\OurChat2\\Client\\Rewriting\\smoothscrolllistwidget.cpp"], "directory": "G:/code/project/OurChat/Client/build/Desktop_Qt_6_5_3_MSVC2019_64bit-Debug/.qtc_clangd", "file": "G:/code/project/OurChat2/Client/Rewriting/smoothscrolllistwidget.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-nologo", "-Zc:wchar_t", "-FS", "-Zc:rvalueCast", "-Zc:inline", "-Zc:strictStrings", "-Zc:<PERSON><PERSON><PERSON>", "-permissive-", "-Zc:__cplusplus", "-Zc:externConstexpr", "-<PERSON>i", "-MDd", "-clang:-std=c++17", "-utf-8", "-W3", "-w34100", "-w34189", "-w44996", "-w44456", "-w44457", "-w44458", "-wd4577", "-wd4467", "-EHsc", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "-fms-compatibility-version=19.29", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-DWIN64", "-DRC_ICONS", "-DQT_DEPRECATED_WARNINGS", "-DQT_QML_DEBUG", "-DQT_OPENGLWIDGETS_LIB", "-DQT_OPENGL_LIB", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IG:\\software\\Qt\\Qt6.5.3\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IG:\\software\\Qt\\Qt6.5.3\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IG:\\code\\project\\OurChat2\\Client", "-IG:\\code\\project\\OurChat2\\Client\\Adding", "-IG:\\code\\project\\OurChat2\\Client\\Chatting", "-IG:\\code\\project\\OurChat2\\Client\\IconSetting", "-IG:\\code\\project\\OurChat2\\Client\\Tools", "-IG:\\code\\project\\OurChat2\\Client\\Rewriting", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtOpenGLWidgets", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtOpenGL", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtWidgets", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtGui", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtNetwork", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtCore", "-IG:\\code\\project\\OurChat\\Client\\build\\Desktop_Qt_6_5_3_MSVC2019_64bit-Debug\\debug", "-IG:\\code\\project\\OurChat\\Client\\build\\Desktop_Qt_6_5_3_MSVC2019_64bit-Debug", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:G:\\software\\Qt\\Qt6.5.3\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\17\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\Community\\VC\\Tools\\MSVC\\14.29.30133\\ATLMFC\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\Community\\VC\\Tools\\MSVC\\14.29.30133\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\NETFXSDK\\4.8\\include\\um", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\ucrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\shared", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\um", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\winrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\cppwinrt", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/TP", "G:\\code\\project\\OurChat2\\Client\\Tools\\imagepreview.cpp"], "directory": "G:/code/project/OurChat/Client/build/Desktop_Qt_6_5_3_MSVC2019_64bit-Debug/.qtc_clangd", "file": "G:/code/project/OurChat2/Client/Tools/imagepreview.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-nologo", "-Zc:wchar_t", "-FS", "-Zc:rvalueCast", "-Zc:inline", "-Zc:strictStrings", "-Zc:<PERSON><PERSON><PERSON>", "-permissive-", "-Zc:__cplusplus", "-Zc:externConstexpr", "-<PERSON>i", "-MDd", "-clang:-std=c++17", "-utf-8", "-W3", "-w34100", "-w34189", "-w44996", "-w44456", "-w44457", "-w44458", "-wd4577", "-wd4467", "-EHsc", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "-fms-compatibility-version=19.29", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-DWIN64", "-DRC_ICONS", "-DQT_DEPRECATED_WARNINGS", "-DQT_QML_DEBUG", "-DQT_OPENGLWIDGETS_LIB", "-DQT_OPENGL_LIB", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IG:\\software\\Qt\\Qt6.5.3\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IG:\\software\\Qt\\Qt6.5.3\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IG:\\code\\project\\OurChat2\\Client", "-IG:\\code\\project\\OurChat2\\Client\\Adding", "-IG:\\code\\project\\OurChat2\\Client\\Chatting", "-IG:\\code\\project\\OurChat2\\Client\\IconSetting", "-IG:\\code\\project\\OurChat2\\Client\\Tools", "-IG:\\code\\project\\OurChat2\\Client\\Rewriting", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtOpenGLWidgets", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtOpenGL", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtWidgets", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtGui", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtNetwork", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtCore", "-IG:\\code\\project\\OurChat\\Client\\build\\Desktop_Qt_6_5_3_MSVC2019_64bit-Debug\\debug", "-IG:\\code\\project\\OurChat\\Client\\build\\Desktop_Qt_6_5_3_MSVC2019_64bit-Debug", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:G:\\software\\Qt\\Qt6.5.3\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\17\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\Community\\VC\\Tools\\MSVC\\14.29.30133\\ATLMFC\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\Community\\VC\\Tools\\MSVC\\14.29.30133\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\NETFXSDK\\4.8\\include\\um", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\ucrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\shared", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\um", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\winrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\cppwinrt", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/TP", "G:\\code\\project\\OurChat2\\Client\\friendinfowidget.cpp"], "directory": "G:/code/project/OurChat/Client/build/Desktop_Qt_6_5_3_MSVC2019_64bit-Debug/.qtc_clangd", "file": "G:/code/project/OurChat2/Client/friendinfowidget.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-nologo", "-Zc:wchar_t", "-FS", "-Zc:rvalueCast", "-Zc:inline", "-Zc:strictStrings", "-Zc:<PERSON><PERSON><PERSON>", "-permissive-", "-Zc:__cplusplus", "-Zc:externConstexpr", "-<PERSON>i", "-MDd", "-clang:-std=c++17", "-utf-8", "-W3", "-w34100", "-w34189", "-w44996", "-w44456", "-w44457", "-w44458", "-wd4577", "-wd4467", "-EHsc", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "-fms-compatibility-version=19.29", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-DWIN64", "-DRC_ICONS", "-DQT_DEPRECATED_WARNINGS", "-DQT_QML_DEBUG", "-DQT_OPENGLWIDGETS_LIB", "-DQT_OPENGL_LIB", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IG:\\software\\Qt\\Qt6.5.3\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IG:\\software\\Qt\\Qt6.5.3\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IG:\\code\\project\\OurChat2\\Client", "-IG:\\code\\project\\OurChat2\\Client\\Adding", "-IG:\\code\\project\\OurChat2\\Client\\Chatting", "-IG:\\code\\project\\OurChat2\\Client\\IconSetting", "-IG:\\code\\project\\OurChat2\\Client\\Tools", "-IG:\\code\\project\\OurChat2\\Client\\Rewriting", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtOpenGLWidgets", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtOpenGL", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtWidgets", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtGui", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtNetwork", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtCore", "-IG:\\code\\project\\OurChat\\Client\\build\\Desktop_Qt_6_5_3_MSVC2019_64bit-Debug\\debug", "-IG:\\code\\project\\OurChat\\Client\\build\\Desktop_Qt_6_5_3_MSVC2019_64bit-Debug", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:G:\\software\\Qt\\Qt6.5.3\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\17\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\Community\\VC\\Tools\\MSVC\\14.29.30133\\ATLMFC\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\Community\\VC\\Tools\\MSVC\\14.29.30133\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\NETFXSDK\\4.8\\include\\um", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\ucrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\shared", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\um", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\winrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\cppwinrt", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/TP", "G:\\code\\project\\OurChat2\\Client\\Rewriting\\frienditem.cpp"], "directory": "G:/code/project/OurChat/Client/build/Desktop_Qt_6_5_3_MSVC2019_64bit-Debug/.qtc_clangd", "file": "G:/code/project/OurChat2/Client/Rewriting/frienditem.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-nologo", "-Zc:wchar_t", "-FS", "-Zc:rvalueCast", "-Zc:inline", "-Zc:strictStrings", "-Zc:<PERSON><PERSON><PERSON>", "-permissive-", "-Zc:__cplusplus", "-Zc:externConstexpr", "-<PERSON>i", "-MDd", "-clang:-std=c++17", "-utf-8", "-W3", "-w34100", "-w34189", "-w44996", "-w44456", "-w44457", "-w44458", "-wd4577", "-wd4467", "-EHsc", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "-fms-compatibility-version=19.29", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-DWIN64", "-DRC_ICONS", "-DQT_DEPRECATED_WARNINGS", "-DQT_QML_DEBUG", "-DQT_OPENGLWIDGETS_LIB", "-DQT_OPENGL_LIB", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IG:\\software\\Qt\\Qt6.5.3\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IG:\\software\\Qt\\Qt6.5.3\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IG:\\code\\project\\OurChat2\\Client", "-IG:\\code\\project\\OurChat2\\Client\\Adding", "-IG:\\code\\project\\OurChat2\\Client\\Chatting", "-IG:\\code\\project\\OurChat2\\Client\\IconSetting", "-IG:\\code\\project\\OurChat2\\Client\\Tools", "-IG:\\code\\project\\OurChat2\\Client\\Rewriting", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtOpenGLWidgets", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtOpenGL", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtWidgets", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtGui", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtNetwork", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtCore", "-IG:\\code\\project\\OurChat\\Client\\build\\Desktop_Qt_6_5_3_MSVC2019_64bit-Debug\\debug", "-IG:\\code\\project\\OurChat\\Client\\build\\Desktop_Qt_6_5_3_MSVC2019_64bit-Debug", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:G:\\software\\Qt\\Qt6.5.3\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\17\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\Community\\VC\\Tools\\MSVC\\14.29.30133\\ATLMFC\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\Community\\VC\\Tools\\MSVC\\14.29.30133\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\NETFXSDK\\4.8\\include\\um", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\ucrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\shared", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\um", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\winrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\cppwinrt", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/TP", "G:\\code\\project\\OurChat2\\Client\\Rewriting\\sendtextedit.cpp"], "directory": "G:/code/project/OurChat/Client/build/Desktop_Qt_6_5_3_MSVC2019_64bit-Debug/.qtc_clangd", "file": "G:/code/project/OurChat2/Client/Rewriting/sendtextedit.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-nologo", "-Zc:wchar_t", "-FS", "-Zc:rvalueCast", "-Zc:inline", "-Zc:strictStrings", "-Zc:<PERSON><PERSON><PERSON>", "-permissive-", "-Zc:__cplusplus", "-Zc:externConstexpr", "-<PERSON>i", "-MDd", "-clang:-std=c++17", "-utf-8", "-W3", "-w34100", "-w34189", "-w44996", "-w44456", "-w44457", "-w44458", "-wd4577", "-wd4467", "-EHsc", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "-fms-compatibility-version=19.29", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-DWIN64", "-DRC_ICONS", "-DQT_DEPRECATED_WARNINGS", "-DQT_QML_DEBUG", "-DQT_OPENGLWIDGETS_LIB", "-DQT_OPENGL_LIB", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IG:\\software\\Qt\\Qt6.5.3\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IG:\\software\\Qt\\Qt6.5.3\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IG:\\code\\project\\OurChat2\\Client", "-IG:\\code\\project\\OurChat2\\Client\\Adding", "-IG:\\code\\project\\OurChat2\\Client\\Chatting", "-IG:\\code\\project\\OurChat2\\Client\\IconSetting", "-IG:\\code\\project\\OurChat2\\Client\\Tools", "-IG:\\code\\project\\OurChat2\\Client\\Rewriting", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtOpenGLWidgets", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtOpenGL", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtWidgets", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtGui", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtNetwork", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtCore", "-IG:\\code\\project\\OurChat\\Client\\build\\Desktop_Qt_6_5_3_MSVC2019_64bit-Debug\\debug", "-IG:\\code\\project\\OurChat\\Client\\build\\Desktop_Qt_6_5_3_MSVC2019_64bit-Debug", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:G:\\software\\Qt\\Qt6.5.3\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\17\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\Community\\VC\\Tools\\MSVC\\14.29.30133\\ATLMFC\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\Community\\VC\\Tools\\MSVC\\14.29.30133\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\NETFXSDK\\4.8\\include\\um", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\ucrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\shared", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\um", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\winrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\cppwinrt", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/TP", "G:\\code\\project\\OurChat2\\Client\\Tools\\stringtool.cpp"], "directory": "G:/code/project/OurChat/Client/build/Desktop_Qt_6_5_3_MSVC2019_64bit-Debug/.qtc_clangd", "file": "G:/code/project/OurChat2/Client/Tools/stringtool.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-nologo", "-Zc:wchar_t", "-FS", "-Zc:rvalueCast", "-Zc:inline", "-Zc:strictStrings", "-Zc:<PERSON><PERSON><PERSON>", "-permissive-", "-Zc:__cplusplus", "-Zc:externConstexpr", "-<PERSON>i", "-MDd", "-clang:-std=c++17", "-utf-8", "-W3", "-w34100", "-w34189", "-w44996", "-w44456", "-w44457", "-w44458", "-wd4577", "-wd4467", "-EHsc", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "-fms-compatibility-version=19.29", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-DWIN64", "-DRC_ICONS", "-DQT_DEPRECATED_WARNINGS", "-DQT_QML_DEBUG", "-DQT_OPENGLWIDGETS_LIB", "-DQT_OPENGL_LIB", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IG:\\software\\Qt\\Qt6.5.3\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IG:\\software\\Qt\\Qt6.5.3\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IG:\\code\\project\\OurChat2\\Client", "-IG:\\code\\project\\OurChat2\\Client\\Adding", "-IG:\\code\\project\\OurChat2\\Client\\Chatting", "-IG:\\code\\project\\OurChat2\\Client\\IconSetting", "-IG:\\code\\project\\OurChat2\\Client\\Tools", "-IG:\\code\\project\\OurChat2\\Client\\Rewriting", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtOpenGLWidgets", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtOpenGL", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtWidgets", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtGui", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtNetwork", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtCore", "-IG:\\code\\project\\OurChat\\Client\\build\\Desktop_Qt_6_5_3_MSVC2019_64bit-Debug\\debug", "-IG:\\code\\project\\OurChat\\Client\\build\\Desktop_Qt_6_5_3_MSVC2019_64bit-Debug", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:G:\\software\\Qt\\Qt6.5.3\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\17\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\Community\\VC\\Tools\\MSVC\\14.29.30133\\ATLMFC\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\Community\\VC\\Tools\\MSVC\\14.29.30133\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\NETFXSDK\\4.8\\include\\um", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\ucrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\shared", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\um", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\winrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\cppwinrt", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/TP", "G:\\code\\project\\OurChat2\\Client\\Tools\\tcpclient.cpp"], "directory": "G:/code/project/OurChat/Client/build/Desktop_Qt_6_5_3_MSVC2019_64bit-Debug/.qtc_clangd", "file": "G:/code/project/OurChat2/Client/Tools/tcpclient.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-nologo", "-Zc:wchar_t", "-FS", "-Zc:rvalueCast", "-Zc:inline", "-Zc:strictStrings", "-Zc:<PERSON><PERSON><PERSON>", "-permissive-", "-Zc:__cplusplus", "-Zc:externConstexpr", "-<PERSON>i", "-MDd", "-clang:-std=c++17", "-utf-8", "-W3", "-w34100", "-w34189", "-w44996", "-w44456", "-w44457", "-w44458", "-wd4577", "-wd4467", "-EHsc", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "-fms-compatibility-version=19.29", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-DWIN64", "-DRC_ICONS", "-DQT_DEPRECATED_WARNINGS", "-DQT_QML_DEBUG", "-DQT_OPENGLWIDGETS_LIB", "-DQT_OPENGL_LIB", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IG:\\software\\Qt\\Qt6.5.3\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IG:\\software\\Qt\\Qt6.5.3\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IG:\\code\\project\\OurChat2\\Client", "-IG:\\code\\project\\OurChat2\\Client\\Adding", "-IG:\\code\\project\\OurChat2\\Client\\Chatting", "-IG:\\code\\project\\OurChat2\\Client\\IconSetting", "-IG:\\code\\project\\OurChat2\\Client\\Tools", "-IG:\\code\\project\\OurChat2\\Client\\Rewriting", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtOpenGLWidgets", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtOpenGL", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtWidgets", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtGui", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtNetwork", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtCore", "-IG:\\code\\project\\OurChat\\Client\\build\\Desktop_Qt_6_5_3_MSVC2019_64bit-Debug\\debug", "-IG:\\code\\project\\OurChat\\Client\\build\\Desktop_Qt_6_5_3_MSVC2019_64bit-Debug", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:G:\\software\\Qt\\Qt6.5.3\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\17\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\Community\\VC\\Tools\\MSVC\\14.29.30133\\ATLMFC\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\Community\\VC\\Tools\\MSVC\\14.29.30133\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\NETFXSDK\\4.8\\include\\um", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\ucrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\shared", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\um", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\winrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\cppwinrt", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/TP", "G:\\code\\project\\OurChat2\\Client\\client.cpp"], "directory": "G:/code/project/OurChat/Client/build/Desktop_Qt_6_5_3_MSVC2019_64bit-Debug/.qtc_clangd", "file": "G:/code/project/OurChat2/Client/client.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-nologo", "-Zc:wchar_t", "-FS", "-Zc:rvalueCast", "-Zc:inline", "-Zc:strictStrings", "-Zc:<PERSON><PERSON><PERSON>", "-permissive-", "-Zc:__cplusplus", "-Zc:externConstexpr", "-<PERSON>i", "-MDd", "-clang:-std=c++17", "-utf-8", "-W3", "-w34100", "-w34189", "-w44996", "-w44456", "-w44457", "-w44458", "-wd4577", "-wd4467", "-EHsc", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "-fms-compatibility-version=19.29", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-DWIN64", "-DRC_ICONS", "-DQT_DEPRECATED_WARNINGS", "-DQT_QML_DEBUG", "-DQT_OPENGLWIDGETS_LIB", "-DQT_OPENGL_LIB", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IG:\\software\\Qt\\Qt6.5.3\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IG:\\software\\Qt\\Qt6.5.3\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IG:\\code\\project\\OurChat2\\Client", "-IG:\\code\\project\\OurChat2\\Client\\Adding", "-IG:\\code\\project\\OurChat2\\Client\\Chatting", "-IG:\\code\\project\\OurChat2\\Client\\IconSetting", "-IG:\\code\\project\\OurChat2\\Client\\Tools", "-IG:\\code\\project\\OurChat2\\Client\\Rewriting", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtOpenGLWidgets", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtOpenGL", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtWidgets", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtGui", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtNetwork", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtCore", "-IG:\\code\\project\\OurChat\\Client\\build\\Desktop_Qt_6_5_3_MSVC2019_64bit-Debug\\debug", "-IG:\\code\\project\\OurChat\\Client\\build\\Desktop_Qt_6_5_3_MSVC2019_64bit-Debug", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:G:\\software\\Qt\\Qt6.5.3\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\17\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\Community\\VC\\Tools\\MSVC\\14.29.30133\\ATLMFC\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\Community\\VC\\Tools\\MSVC\\14.29.30133\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\NETFXSDK\\4.8\\include\\um", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\ucrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\shared", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\um", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\winrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\cppwinrt", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/TP", "G:\\code\\project\\OurChat2\\Client\\logging.cpp"], "directory": "G:/code/project/OurChat/Client/build/Desktop_Qt_6_5_3_MSVC2019_64bit-Debug/.qtc_clangd", "file": "G:/code/project/OurChat2/Client/logging.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-nologo", "-Zc:wchar_t", "-FS", "-Zc:rvalueCast", "-Zc:inline", "-Zc:strictStrings", "-Zc:<PERSON><PERSON><PERSON>", "-permissive-", "-Zc:__cplusplus", "-Zc:externConstexpr", "-<PERSON>i", "-MDd", "-clang:-std=c++17", "-utf-8", "-W3", "-w34100", "-w34189", "-w44996", "-w44456", "-w44457", "-w44458", "-wd4577", "-wd4467", "-EHsc", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "-fms-compatibility-version=19.29", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-DWIN64", "-DRC_ICONS", "-DQT_DEPRECATED_WARNINGS", "-DQT_QML_DEBUG", "-DQT_OPENGLWIDGETS_LIB", "-DQT_OPENGL_LIB", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IG:\\software\\Qt\\Qt6.5.3\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IG:\\software\\Qt\\Qt6.5.3\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IG:\\code\\project\\OurChat2\\Client", "-IG:\\code\\project\\OurChat2\\Client\\Adding", "-IG:\\code\\project\\OurChat2\\Client\\Chatting", "-IG:\\code\\project\\OurChat2\\Client\\IconSetting", "-IG:\\code\\project\\OurChat2\\Client\\Tools", "-IG:\\code\\project\\OurChat2\\Client\\Rewriting", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtOpenGLWidgets", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtOpenGL", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtWidgets", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtGui", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtNetwork", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtCore", "-IG:\\code\\project\\OurChat\\Client\\build\\Desktop_Qt_6_5_3_MSVC2019_64bit-Debug\\debug", "-IG:\\code\\project\\OurChat\\Client\\build\\Desktop_Qt_6_5_3_MSVC2019_64bit-Debug", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:G:\\software\\Qt\\Qt6.5.3\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\17\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\Community\\VC\\Tools\\MSVC\\14.29.30133\\ATLMFC\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\Community\\VC\\Tools\\MSVC\\14.29.30133\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\NETFXSDK\\4.8\\include\\um", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\ucrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\shared", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\um", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\winrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\cppwinrt", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/TP", "G:\\code\\project\\OurChat2\\Client\\main.cpp"], "directory": "G:/code/project/OurChat/Client/build/Desktop_Qt_6_5_3_MSVC2019_64bit-Debug/.qtc_clangd", "file": "G:/code/project/OurChat2/Client/main.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-nologo", "-Zc:wchar_t", "-FS", "-Zc:rvalueCast", "-Zc:inline", "-Zc:strictStrings", "-Zc:<PERSON><PERSON><PERSON>", "-permissive-", "-Zc:__cplusplus", "-Zc:externConstexpr", "-<PERSON>i", "-MDd", "-clang:-std=c++17", "-utf-8", "-W3", "-w34100", "-w34189", "-w44996", "-w44456", "-w44457", "-w44458", "-wd4577", "-wd4467", "-EHsc", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "-fms-compatibility-version=19.29", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-DWIN64", "-DRC_ICONS", "-DQT_DEPRECATED_WARNINGS", "-DQT_QML_DEBUG", "-DQT_OPENGLWIDGETS_LIB", "-DQT_OPENGL_LIB", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IG:\\software\\Qt\\Qt6.5.3\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IG:\\software\\Qt\\Qt6.5.3\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IG:\\code\\project\\OurChat2\\Client", "-IG:\\code\\project\\OurChat2\\Client\\Adding", "-IG:\\code\\project\\OurChat2\\Client\\Chatting", "-IG:\\code\\project\\OurChat2\\Client\\IconSetting", "-IG:\\code\\project\\OurChat2\\Client\\Tools", "-IG:\\code\\project\\OurChat2\\Client\\Rewriting", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtOpenGLWidgets", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtOpenGL", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtWidgets", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtGui", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtNetwork", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtCore", "-IG:\\code\\project\\OurChat\\Client\\build\\Desktop_Qt_6_5_3_MSVC2019_64bit-Debug\\debug", "-IG:\\code\\project\\OurChat\\Client\\build\\Desktop_Qt_6_5_3_MSVC2019_64bit-Debug", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:G:\\software\\Qt\\Qt6.5.3\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\17\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\Community\\VC\\Tools\\MSVC\\14.29.30133\\ATLMFC\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\Community\\VC\\Tools\\MSVC\\14.29.30133\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\NETFXSDK\\4.8\\include\\um", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\ucrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\shared", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\um", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\winrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\cppwinrt", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/TP", "G:\\code\\project\\OurChat2\\Client\\selfinfowidget.cpp"], "directory": "G:/code/project/OurChat/Client/build/Desktop_Qt_6_5_3_MSVC2019_64bit-Debug/.qtc_clangd", "file": "G:/code/project/OurChat2/Client/selfinfowidget.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-nologo", "-Zc:wchar_t", "-FS", "-Zc:rvalueCast", "-Zc:inline", "-Zc:strictStrings", "-Zc:<PERSON><PERSON><PERSON>", "-permissive-", "-Zc:__cplusplus", "-Zc:externConstexpr", "-<PERSON>i", "-MDd", "-clang:-std=c++17", "-utf-8", "-W3", "-w34100", "-w34189", "-w44996", "-w44456", "-w44457", "-w44458", "-wd4577", "-wd4467", "-EHsc", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "-fms-compatibility-version=19.29", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-DWIN64", "-DRC_ICONS", "-DQT_DEPRECATED_WARNINGS", "-DQT_QML_DEBUG", "-DQT_OPENGLWIDGETS_LIB", "-DQT_OPENGL_LIB", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IG:\\software\\Qt\\Qt6.5.3\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IG:\\software\\Qt\\Qt6.5.3\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IG:\\code\\project\\OurChat2\\Client", "-IG:\\code\\project\\OurChat2\\Client\\Adding", "-IG:\\code\\project\\OurChat2\\Client\\Chatting", "-IG:\\code\\project\\OurChat2\\Client\\IconSetting", "-IG:\\code\\project\\OurChat2\\Client\\Tools", "-IG:\\code\\project\\OurChat2\\Client\\Rewriting", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtOpenGLWidgets", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtOpenGL", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtWidgets", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtGui", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtNetwork", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtCore", "-IG:\\code\\project\\OurChat\\Client\\build\\Desktop_Qt_6_5_3_MSVC2019_64bit-Debug\\debug", "-IG:\\code\\project\\OurChat\\Client\\build\\Desktop_Qt_6_5_3_MSVC2019_64bit-Debug", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:G:\\software\\Qt\\Qt6.5.3\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\17\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\Community\\VC\\Tools\\MSVC\\14.29.30133\\ATLMFC\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\Community\\VC\\Tools\\MSVC\\14.29.30133\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\NETFXSDK\\4.8\\include\\um", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\ucrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\shared", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\um", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\winrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\cppwinrt", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/TP", "G:\\code\\project\\OurChat2\\Client\\Adding\\addfriend.h"], "directory": "G:/code/project/OurChat/Client/build/Desktop_Qt_6_5_3_MSVC2019_64bit-Debug/.qtc_clangd", "file": "G:/code/project/OurChat2/Client/Adding/addfriend.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-nologo", "-Zc:wchar_t", "-FS", "-Zc:rvalueCast", "-Zc:inline", "-Zc:strictStrings", "-Zc:<PERSON><PERSON><PERSON>", "-permissive-", "-Zc:__cplusplus", "-Zc:externConstexpr", "-<PERSON>i", "-MDd", "-clang:-std=c++17", "-utf-8", "-W3", "-w34100", "-w34189", "-w44996", "-w44456", "-w44457", "-w44458", "-wd4577", "-wd4467", "-EHsc", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "-fms-compatibility-version=19.29", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-DWIN64", "-DRC_ICONS", "-DQT_DEPRECATED_WARNINGS", "-DQT_QML_DEBUG", "-DQT_OPENGLWIDGETS_LIB", "-DQT_OPENGL_LIB", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IG:\\software\\Qt\\Qt6.5.3\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IG:\\software\\Qt\\Qt6.5.3\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IG:\\code\\project\\OurChat2\\Client", "-IG:\\code\\project\\OurChat2\\Client\\Adding", "-IG:\\code\\project\\OurChat2\\Client\\Chatting", "-IG:\\code\\project\\OurChat2\\Client\\IconSetting", "-IG:\\code\\project\\OurChat2\\Client\\Tools", "-IG:\\code\\project\\OurChat2\\Client\\Rewriting", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtOpenGLWidgets", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtOpenGL", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtWidgets", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtGui", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtNetwork", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtCore", "-IG:\\code\\project\\OurChat\\Client\\build\\Desktop_Qt_6_5_3_MSVC2019_64bit-Debug\\debug", "-IG:\\code\\project\\OurChat\\Client\\build\\Desktop_Qt_6_5_3_MSVC2019_64bit-Debug", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:G:\\software\\Qt\\Qt6.5.3\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\17\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\Community\\VC\\Tools\\MSVC\\14.29.30133\\ATLMFC\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\Community\\VC\\Tools\\MSVC\\14.29.30133\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\NETFXSDK\\4.8\\include\\um", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\ucrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\shared", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\um", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\winrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\cppwinrt", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/TP", "G:\\code\\project\\OurChat2\\Client\\Adding\\systemmessage.h"], "directory": "G:/code/project/OurChat/Client/build/Desktop_Qt_6_5_3_MSVC2019_64bit-Debug/.qtc_clangd", "file": "G:/code/project/OurChat2/Client/Adding/systemmessage.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-nologo", "-Zc:wchar_t", "-FS", "-Zc:rvalueCast", "-Zc:inline", "-Zc:strictStrings", "-Zc:<PERSON><PERSON><PERSON>", "-permissive-", "-Zc:__cplusplus", "-Zc:externConstexpr", "-<PERSON>i", "-MDd", "-clang:-std=c++17", "-utf-8", "-W3", "-w34100", "-w34189", "-w44996", "-w44456", "-w44457", "-w44458", "-wd4577", "-wd4467", "-EHsc", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "-fms-compatibility-version=19.29", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-DWIN64", "-DRC_ICONS", "-DQT_DEPRECATED_WARNINGS", "-DQT_QML_DEBUG", "-DQT_OPENGLWIDGETS_LIB", "-DQT_OPENGL_LIB", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IG:\\software\\Qt\\Qt6.5.3\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IG:\\software\\Qt\\Qt6.5.3\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IG:\\code\\project\\OurChat2\\Client", "-IG:\\code\\project\\OurChat2\\Client\\Adding", "-IG:\\code\\project\\OurChat2\\Client\\Chatting", "-IG:\\code\\project\\OurChat2\\Client\\IconSetting", "-IG:\\code\\project\\OurChat2\\Client\\Tools", "-IG:\\code\\project\\OurChat2\\Client\\Rewriting", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtOpenGLWidgets", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtOpenGL", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtWidgets", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtGui", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtNetwork", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtCore", "-IG:\\code\\project\\OurChat\\Client\\build\\Desktop_Qt_6_5_3_MSVC2019_64bit-Debug\\debug", "-IG:\\code\\project\\OurChat\\Client\\build\\Desktop_Qt_6_5_3_MSVC2019_64bit-Debug", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:G:\\software\\Qt\\Qt6.5.3\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\17\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\Community\\VC\\Tools\\MSVC\\14.29.30133\\ATLMFC\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\Community\\VC\\Tools\\MSVC\\14.29.30133\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\NETFXSDK\\4.8\\include\\um", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\ucrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\shared", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\um", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\winrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\cppwinrt", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/TP", "G:\\code\\project\\OurChat2\\Client\\Adding\\verificationitem.h"], "directory": "G:/code/project/OurChat/Client/build/Desktop_Qt_6_5_3_MSVC2019_64bit-Debug/.qtc_clangd", "file": "G:/code/project/OurChat2/Client/Adding/verificationitem.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-nologo", "-Zc:wchar_t", "-FS", "-Zc:rvalueCast", "-Zc:inline", "-Zc:strictStrings", "-Zc:<PERSON><PERSON><PERSON>", "-permissive-", "-Zc:__cplusplus", "-Zc:externConstexpr", "-<PERSON>i", "-MDd", "-clang:-std=c++17", "-utf-8", "-W3", "-w34100", "-w34189", "-w44996", "-w44456", "-w44457", "-w44458", "-wd4577", "-wd4467", "-EHsc", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "-fms-compatibility-version=19.29", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-DWIN64", "-DRC_ICONS", "-DQT_DEPRECATED_WARNINGS", "-DQT_QML_DEBUG", "-DQT_OPENGLWIDGETS_LIB", "-DQT_OPENGL_LIB", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IG:\\software\\Qt\\Qt6.5.3\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IG:\\software\\Qt\\Qt6.5.3\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IG:\\code\\project\\OurChat2\\Client", "-IG:\\code\\project\\OurChat2\\Client\\Adding", "-IG:\\code\\project\\OurChat2\\Client\\Chatting", "-IG:\\code\\project\\OurChat2\\Client\\IconSetting", "-IG:\\code\\project\\OurChat2\\Client\\Tools", "-IG:\\code\\project\\OurChat2\\Client\\Rewriting", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtOpenGLWidgets", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtOpenGL", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtWidgets", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtGui", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtNetwork", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtCore", "-IG:\\code\\project\\OurChat\\Client\\build\\Desktop_Qt_6_5_3_MSVC2019_64bit-Debug\\debug", "-IG:\\code\\project\\OurChat\\Client\\build\\Desktop_Qt_6_5_3_MSVC2019_64bit-Debug", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:G:\\software\\Qt\\Qt6.5.3\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\17\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\Community\\VC\\Tools\\MSVC\\14.29.30133\\ATLMFC\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\Community\\VC\\Tools\\MSVC\\14.29.30133\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\NETFXSDK\\4.8\\include\\um", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\ucrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\shared", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\um", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\winrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\cppwinrt", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/TP", "G:\\code\\project\\OurChat2\\Client\\Chatting\\chatlistwidget.h"], "directory": "G:/code/project/OurChat/Client/build/Desktop_Qt_6_5_3_MSVC2019_64bit-Debug/.qtc_clangd", "file": "G:/code/project/OurChat2/Client/Chatting/chatlistwidget.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-nologo", "-Zc:wchar_t", "-FS", "-Zc:rvalueCast", "-Zc:inline", "-Zc:strictStrings", "-Zc:<PERSON><PERSON><PERSON>", "-permissive-", "-Zc:__cplusplus", "-Zc:externConstexpr", "-<PERSON>i", "-MDd", "-clang:-std=c++17", "-utf-8", "-W3", "-w34100", "-w34189", "-w44996", "-w44456", "-w44457", "-w44458", "-wd4577", "-wd4467", "-EHsc", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "-fms-compatibility-version=19.29", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-DWIN64", "-DRC_ICONS", "-DQT_DEPRECATED_WARNINGS", "-DQT_QML_DEBUG", "-DQT_OPENGLWIDGETS_LIB", "-DQT_OPENGL_LIB", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IG:\\software\\Qt\\Qt6.5.3\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IG:\\software\\Qt\\Qt6.5.3\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IG:\\code\\project\\OurChat2\\Client", "-IG:\\code\\project\\OurChat2\\Client\\Adding", "-IG:\\code\\project\\OurChat2\\Client\\Chatting", "-IG:\\code\\project\\OurChat2\\Client\\IconSetting", "-IG:\\code\\project\\OurChat2\\Client\\Tools", "-IG:\\code\\project\\OurChat2\\Client\\Rewriting", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtOpenGLWidgets", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtOpenGL", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtWidgets", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtGui", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtNetwork", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtCore", "-IG:\\code\\project\\OurChat\\Client\\build\\Desktop_Qt_6_5_3_MSVC2019_64bit-Debug\\debug", "-IG:\\code\\project\\OurChat\\Client\\build\\Desktop_Qt_6_5_3_MSVC2019_64bit-Debug", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:G:\\software\\Qt\\Qt6.5.3\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\17\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\Community\\VC\\Tools\\MSVC\\14.29.30133\\ATLMFC\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\Community\\VC\\Tools\\MSVC\\14.29.30133\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\NETFXSDK\\4.8\\include\\um", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\ucrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\shared", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\um", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\winrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\cppwinrt", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/TP", "G:\\code\\project\\OurChat2\\Client\\Chatting\\chatwindow.h"], "directory": "G:/code/project/OurChat/Client/build/Desktop_Qt_6_5_3_MSVC2019_64bit-Debug/.qtc_clangd", "file": "G:/code/project/OurChat2/Client/Chatting/chatwindow.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-nologo", "-Zc:wchar_t", "-FS", "-Zc:rvalueCast", "-Zc:inline", "-Zc:strictStrings", "-Zc:<PERSON><PERSON><PERSON>", "-permissive-", "-Zc:__cplusplus", "-Zc:externConstexpr", "-<PERSON>i", "-MDd", "-clang:-std=c++17", "-utf-8", "-W3", "-w34100", "-w34189", "-w44996", "-w44456", "-w44457", "-w44458", "-wd4577", "-wd4467", "-EHsc", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "-fms-compatibility-version=19.29", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-DWIN64", "-DRC_ICONS", "-DQT_DEPRECATED_WARNINGS", "-DQT_QML_DEBUG", "-DQT_OPENGLWIDGETS_LIB", "-DQT_OPENGL_LIB", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IG:\\software\\Qt\\Qt6.5.3\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IG:\\software\\Qt\\Qt6.5.3\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IG:\\code\\project\\OurChat2\\Client", "-IG:\\code\\project\\OurChat2\\Client\\Adding", "-IG:\\code\\project\\OurChat2\\Client\\Chatting", "-IG:\\code\\project\\OurChat2\\Client\\IconSetting", "-IG:\\code\\project\\OurChat2\\Client\\Tools", "-IG:\\code\\project\\OurChat2\\Client\\Rewriting", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtOpenGLWidgets", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtOpenGL", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtWidgets", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtGui", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtNetwork", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtCore", "-IG:\\code\\project\\OurChat\\Client\\build\\Desktop_Qt_6_5_3_MSVC2019_64bit-Debug\\debug", "-IG:\\code\\project\\OurChat\\Client\\build\\Desktop_Qt_6_5_3_MSVC2019_64bit-Debug", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:G:\\software\\Qt\\Qt6.5.3\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\17\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\Community\\VC\\Tools\\MSVC\\14.29.30133\\ATLMFC\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\Community\\VC\\Tools\\MSVC\\14.29.30133\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\NETFXSDK\\4.8\\include\\um", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\ucrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\shared", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\um", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\winrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\cppwinrt", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/TP", "G:\\code\\project\\OurChat2\\Client\\Chatting\\emojiselector.h"], "directory": "G:/code/project/OurChat/Client/build/Desktop_Qt_6_5_3_MSVC2019_64bit-Debug/.qtc_clangd", "file": "G:/code/project/OurChat2/Client/Chatting/emojiselector.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-nologo", "-Zc:wchar_t", "-FS", "-Zc:rvalueCast", "-Zc:inline", "-Zc:strictStrings", "-Zc:<PERSON><PERSON><PERSON>", "-permissive-", "-Zc:__cplusplus", "-Zc:externConstexpr", "-<PERSON>i", "-MDd", "-clang:-std=c++17", "-utf-8", "-W3", "-w34100", "-w34189", "-w44996", "-w44456", "-w44457", "-w44458", "-wd4577", "-wd4467", "-EHsc", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "-fms-compatibility-version=19.29", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-DWIN64", "-DRC_ICONS", "-DQT_DEPRECATED_WARNINGS", "-DQT_QML_DEBUG", "-DQT_OPENGLWIDGETS_LIB", "-DQT_OPENGL_LIB", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IG:\\software\\Qt\\Qt6.5.3\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IG:\\software\\Qt\\Qt6.5.3\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IG:\\code\\project\\OurChat2\\Client", "-IG:\\code\\project\\OurChat2\\Client\\Adding", "-IG:\\code\\project\\OurChat2\\Client\\Chatting", "-IG:\\code\\project\\OurChat2\\Client\\IconSetting", "-IG:\\code\\project\\OurChat2\\Client\\Tools", "-IG:\\code\\project\\OurChat2\\Client\\Rewriting", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtOpenGLWidgets", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtOpenGL", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtWidgets", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtGui", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtNetwork", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtCore", "-IG:\\code\\project\\OurChat\\Client\\build\\Desktop_Qt_6_5_3_MSVC2019_64bit-Debug\\debug", "-IG:\\code\\project\\OurChat\\Client\\build\\Desktop_Qt_6_5_3_MSVC2019_64bit-Debug", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:G:\\software\\Qt\\Qt6.5.3\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\17\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\Community\\VC\\Tools\\MSVC\\14.29.30133\\ATLMFC\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\Community\\VC\\Tools\\MSVC\\14.29.30133\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\NETFXSDK\\4.8\\include\\um", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\ucrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\shared", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\um", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\winrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\cppwinrt", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/TP", "G:\\code\\project\\OurChat2\\Client\\IconSetting\\iconselect.h"], "directory": "G:/code/project/OurChat/Client/build/Desktop_Qt_6_5_3_MSVC2019_64bit-Debug/.qtc_clangd", "file": "G:/code/project/OurChat2/Client/IconSetting/iconselect.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-nologo", "-Zc:wchar_t", "-FS", "-Zc:rvalueCast", "-Zc:inline", "-Zc:strictStrings", "-Zc:<PERSON><PERSON><PERSON>", "-permissive-", "-Zc:__cplusplus", "-Zc:externConstexpr", "-<PERSON>i", "-MDd", "-clang:-std=c++17", "-utf-8", "-W3", "-w34100", "-w34189", "-w44996", "-w44456", "-w44457", "-w44458", "-wd4577", "-wd4467", "-EHsc", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "-fms-compatibility-version=19.29", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-DWIN64", "-DRC_ICONS", "-DQT_DEPRECATED_WARNINGS", "-DQT_QML_DEBUG", "-DQT_OPENGLWIDGETS_LIB", "-DQT_OPENGL_LIB", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IG:\\software\\Qt\\Qt6.5.3\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IG:\\software\\Qt\\Qt6.5.3\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IG:\\code\\project\\OurChat2\\Client", "-IG:\\code\\project\\OurChat2\\Client\\Adding", "-IG:\\code\\project\\OurChat2\\Client\\Chatting", "-IG:\\code\\project\\OurChat2\\Client\\IconSetting", "-IG:\\code\\project\\OurChat2\\Client\\Tools", "-IG:\\code\\project\\OurChat2\\Client\\Rewriting", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtOpenGLWidgets", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtOpenGL", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtWidgets", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtGui", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtNetwork", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtCore", "-IG:\\code\\project\\OurChat\\Client\\build\\Desktop_Qt_6_5_3_MSVC2019_64bit-Debug\\debug", "-IG:\\code\\project\\OurChat\\Client\\build\\Desktop_Qt_6_5_3_MSVC2019_64bit-Debug", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:G:\\software\\Qt\\Qt6.5.3\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\17\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\Community\\VC\\Tools\\MSVC\\14.29.30133\\ATLMFC\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\Community\\VC\\Tools\\MSVC\\14.29.30133\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\NETFXSDK\\4.8\\include\\um", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\ucrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\shared", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\um", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\winrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\cppwinrt", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/TP", "G:\\code\\project\\OurChat2\\Client\\Rewriting\\friendiconlabel.h"], "directory": "G:/code/project/OurChat/Client/build/Desktop_Qt_6_5_3_MSVC2019_64bit-Debug/.qtc_clangd", "file": "G:/code/project/OurChat2/Client/Rewriting/friendiconlabel.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-nologo", "-Zc:wchar_t", "-FS", "-Zc:rvalueCast", "-Zc:inline", "-Zc:strictStrings", "-Zc:<PERSON><PERSON><PERSON>", "-permissive-", "-Zc:__cplusplus", "-Zc:externConstexpr", "-<PERSON>i", "-MDd", "-clang:-std=c++17", "-utf-8", "-W3", "-w34100", "-w34189", "-w44996", "-w44456", "-w44457", "-w44458", "-wd4577", "-wd4467", "-EHsc", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "-fms-compatibility-version=19.29", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-DWIN64", "-DRC_ICONS", "-DQT_DEPRECATED_WARNINGS", "-DQT_QML_DEBUG", "-DQT_OPENGLWIDGETS_LIB", "-DQT_OPENGL_LIB", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IG:\\software\\Qt\\Qt6.5.3\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IG:\\software\\Qt\\Qt6.5.3\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IG:\\code\\project\\OurChat2\\Client", "-IG:\\code\\project\\OurChat2\\Client\\Adding", "-IG:\\code\\project\\OurChat2\\Client\\Chatting", "-IG:\\code\\project\\OurChat2\\Client\\IconSetting", "-IG:\\code\\project\\OurChat2\\Client\\Tools", "-IG:\\code\\project\\OurChat2\\Client\\Rewriting", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtOpenGLWidgets", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtOpenGL", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtWidgets", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtGui", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtNetwork", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtCore", "-IG:\\code\\project\\OurChat\\Client\\build\\Desktop_Qt_6_5_3_MSVC2019_64bit-Debug\\debug", "-IG:\\code\\project\\OurChat\\Client\\build\\Desktop_Qt_6_5_3_MSVC2019_64bit-Debug", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:G:\\software\\Qt\\Qt6.5.3\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\17\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\Community\\VC\\Tools\\MSVC\\14.29.30133\\ATLMFC\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\Community\\VC\\Tools\\MSVC\\14.29.30133\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\NETFXSDK\\4.8\\include\\um", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\ucrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\shared", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\um", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\winrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\cppwinrt", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/TP", "G:\\code\\project\\OurChat2\\Client\\Rewriting\\frienditem.h"], "directory": "G:/code/project/OurChat/Client/build/Desktop_Qt_6_5_3_MSVC2019_64bit-Debug/.qtc_clangd", "file": "G:/code/project/OurChat2/Client/Rewriting/frienditem.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-nologo", "-Zc:wchar_t", "-FS", "-Zc:rvalueCast", "-Zc:inline", "-Zc:strictStrings", "-Zc:<PERSON><PERSON><PERSON>", "-permissive-", "-Zc:__cplusplus", "-Zc:externConstexpr", "-<PERSON>i", "-MDd", "-clang:-std=c++17", "-utf-8", "-W3", "-w34100", "-w34189", "-w44996", "-w44456", "-w44457", "-w44458", "-wd4577", "-wd4467", "-EHsc", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "-fms-compatibility-version=19.29", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-DWIN64", "-DRC_ICONS", "-DQT_DEPRECATED_WARNINGS", "-DQT_QML_DEBUG", "-DQT_OPENGLWIDGETS_LIB", "-DQT_OPENGL_LIB", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IG:\\software\\Qt\\Qt6.5.3\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IG:\\software\\Qt\\Qt6.5.3\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IG:\\code\\project\\OurChat2\\Client", "-IG:\\code\\project\\OurChat2\\Client\\Adding", "-IG:\\code\\project\\OurChat2\\Client\\Chatting", "-IG:\\code\\project\\OurChat2\\Client\\IconSetting", "-IG:\\code\\project\\OurChat2\\Client\\Tools", "-IG:\\code\\project\\OurChat2\\Client\\Rewriting", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtOpenGLWidgets", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtOpenGL", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtWidgets", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtGui", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtNetwork", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtCore", "-IG:\\code\\project\\OurChat\\Client\\build\\Desktop_Qt_6_5_3_MSVC2019_64bit-Debug\\debug", "-IG:\\code\\project\\OurChat\\Client\\build\\Desktop_Qt_6_5_3_MSVC2019_64bit-Debug", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:G:\\software\\Qt\\Qt6.5.3\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\17\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\Community\\VC\\Tools\\MSVC\\14.29.30133\\ATLMFC\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\Community\\VC\\Tools\\MSVC\\14.29.30133\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\NETFXSDK\\4.8\\include\\um", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\ucrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\shared", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\um", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\winrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\cppwinrt", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/TP", "G:\\code\\project\\OurChat2\\Client\\Rewriting\\sendtextedit.h"], "directory": "G:/code/project/OurChat/Client/build/Desktop_Qt_6_5_3_MSVC2019_64bit-Debug/.qtc_clangd", "file": "G:/code/project/OurChat2/Client/Rewriting/sendtextedit.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-nologo", "-Zc:wchar_t", "-FS", "-Zc:rvalueCast", "-Zc:inline", "-Zc:strictStrings", "-Zc:<PERSON><PERSON><PERSON>", "-permissive-", "-Zc:__cplusplus", "-Zc:externConstexpr", "-<PERSON>i", "-MDd", "-clang:-std=c++17", "-utf-8", "-W3", "-w34100", "-w34189", "-w44996", "-w44456", "-w44457", "-w44458", "-wd4577", "-wd4467", "-EHsc", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "-fms-compatibility-version=19.29", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-DWIN64", "-DRC_ICONS", "-DQT_DEPRECATED_WARNINGS", "-DQT_QML_DEBUG", "-DQT_OPENGLWIDGETS_LIB", "-DQT_OPENGL_LIB", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IG:\\software\\Qt\\Qt6.5.3\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IG:\\software\\Qt\\Qt6.5.3\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IG:\\code\\project\\OurChat2\\Client", "-IG:\\code\\project\\OurChat2\\Client\\Adding", "-IG:\\code\\project\\OurChat2\\Client\\Chatting", "-IG:\\code\\project\\OurChat2\\Client\\IconSetting", "-IG:\\code\\project\\OurChat2\\Client\\Tools", "-IG:\\code\\project\\OurChat2\\Client\\Rewriting", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtOpenGLWidgets", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtOpenGL", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtWidgets", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtGui", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtNetwork", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtCore", "-IG:\\code\\project\\OurChat\\Client\\build\\Desktop_Qt_6_5_3_MSVC2019_64bit-Debug\\debug", "-IG:\\code\\project\\OurChat\\Client\\build\\Desktop_Qt_6_5_3_MSVC2019_64bit-Debug", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:G:\\software\\Qt\\Qt6.5.3\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\17\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\Community\\VC\\Tools\\MSVC\\14.29.30133\\ATLMFC\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\Community\\VC\\Tools\\MSVC\\14.29.30133\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\NETFXSDK\\4.8\\include\\um", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\ucrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\shared", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\um", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\winrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\cppwinrt", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/TP", "G:\\code\\project\\OurChat2\\Client\\Rewriting\\smoothscrolllistwidget.h"], "directory": "G:/code/project/OurChat/Client/build/Desktop_Qt_6_5_3_MSVC2019_64bit-Debug/.qtc_clangd", "file": "G:/code/project/OurChat2/Client/Rewriting/smoothscrolllistwidget.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-nologo", "-Zc:wchar_t", "-FS", "-Zc:rvalueCast", "-Zc:inline", "-Zc:strictStrings", "-Zc:<PERSON><PERSON><PERSON>", "-permissive-", "-Zc:__cplusplus", "-Zc:externConstexpr", "-<PERSON>i", "-MDd", "-clang:-std=c++17", "-utf-8", "-W3", "-w34100", "-w34189", "-w44996", "-w44456", "-w44457", "-w44458", "-wd4577", "-wd4467", "-EHsc", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "-fms-compatibility-version=19.29", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-DWIN64", "-DRC_ICONS", "-DQT_DEPRECATED_WARNINGS", "-DQT_QML_DEBUG", "-DQT_OPENGLWIDGETS_LIB", "-DQT_OPENGL_LIB", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IG:\\software\\Qt\\Qt6.5.3\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IG:\\software\\Qt\\Qt6.5.3\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IG:\\code\\project\\OurChat2\\Client", "-IG:\\code\\project\\OurChat2\\Client\\Adding", "-IG:\\code\\project\\OurChat2\\Client\\Chatting", "-IG:\\code\\project\\OurChat2\\Client\\IconSetting", "-IG:\\code\\project\\OurChat2\\Client\\Tools", "-IG:\\code\\project\\OurChat2\\Client\\Rewriting", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtOpenGLWidgets", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtOpenGL", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtWidgets", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtGui", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtNetwork", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtCore", "-IG:\\code\\project\\OurChat\\Client\\build\\Desktop_Qt_6_5_3_MSVC2019_64bit-Debug\\debug", "-IG:\\code\\project\\OurChat\\Client\\build\\Desktop_Qt_6_5_3_MSVC2019_64bit-Debug", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:G:\\software\\Qt\\Qt6.5.3\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\17\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\Community\\VC\\Tools\\MSVC\\14.29.30133\\ATLMFC\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\Community\\VC\\Tools\\MSVC\\14.29.30133\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\NETFXSDK\\4.8\\include\\um", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\ucrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\shared", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\um", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\winrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\cppwinrt", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/TP", "G:\\code\\project\\OurChat2\\Client\\Tools\\imagepreview.h"], "directory": "G:/code/project/OurChat/Client/build/Desktop_Qt_6_5_3_MSVC2019_64bit-Debug/.qtc_clangd", "file": "G:/code/project/OurChat2/Client/Tools/imagepreview.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-nologo", "-Zc:wchar_t", "-FS", "-Zc:rvalueCast", "-Zc:inline", "-Zc:strictStrings", "-Zc:<PERSON><PERSON><PERSON>", "-permissive-", "-Zc:__cplusplus", "-Zc:externConstexpr", "-<PERSON>i", "-MDd", "-clang:-std=c++17", "-utf-8", "-W3", "-w34100", "-w34189", "-w44996", "-w44456", "-w44457", "-w44458", "-wd4577", "-wd4467", "-EHsc", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "-fms-compatibility-version=19.29", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-DWIN64", "-DRC_ICONS", "-DQT_DEPRECATED_WARNINGS", "-DQT_QML_DEBUG", "-DQT_OPENGLWIDGETS_LIB", "-DQT_OPENGL_LIB", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IG:\\software\\Qt\\Qt6.5.3\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IG:\\software\\Qt\\Qt6.5.3\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IG:\\code\\project\\OurChat2\\Client", "-IG:\\code\\project\\OurChat2\\Client\\Adding", "-IG:\\code\\project\\OurChat2\\Client\\Chatting", "-IG:\\code\\project\\OurChat2\\Client\\IconSetting", "-IG:\\code\\project\\OurChat2\\Client\\Tools", "-IG:\\code\\project\\OurChat2\\Client\\Rewriting", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtOpenGLWidgets", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtOpenGL", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtWidgets", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtGui", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtNetwork", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtCore", "-IG:\\code\\project\\OurChat\\Client\\build\\Desktop_Qt_6_5_3_MSVC2019_64bit-Debug\\debug", "-IG:\\code\\project\\OurChat\\Client\\build\\Desktop_Qt_6_5_3_MSVC2019_64bit-Debug", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:G:\\software\\Qt\\Qt6.5.3\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\17\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\Community\\VC\\Tools\\MSVC\\14.29.30133\\ATLMFC\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\Community\\VC\\Tools\\MSVC\\14.29.30133\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\NETFXSDK\\4.8\\include\\um", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\ucrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\shared", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\um", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\winrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\cppwinrt", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/TP", "G:\\code\\project\\OurChat2\\Client\\Tools\\stringtool.h"], "directory": "G:/code/project/OurChat/Client/build/Desktop_Qt_6_5_3_MSVC2019_64bit-Debug/.qtc_clangd", "file": "G:/code/project/OurChat2/Client/Tools/stringtool.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-nologo", "-Zc:wchar_t", "-FS", "-Zc:rvalueCast", "-Zc:inline", "-Zc:strictStrings", "-Zc:<PERSON><PERSON><PERSON>", "-permissive-", "-Zc:__cplusplus", "-Zc:externConstexpr", "-<PERSON>i", "-MDd", "-clang:-std=c++17", "-utf-8", "-W3", "-w34100", "-w34189", "-w44996", "-w44456", "-w44457", "-w44458", "-wd4577", "-wd4467", "-EHsc", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "-fms-compatibility-version=19.29", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-DWIN64", "-DRC_ICONS", "-DQT_DEPRECATED_WARNINGS", "-DQT_QML_DEBUG", "-DQT_OPENGLWIDGETS_LIB", "-DQT_OPENGL_LIB", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IG:\\software\\Qt\\Qt6.5.3\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IG:\\software\\Qt\\Qt6.5.3\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IG:\\code\\project\\OurChat2\\Client", "-IG:\\code\\project\\OurChat2\\Client\\Adding", "-IG:\\code\\project\\OurChat2\\Client\\Chatting", "-IG:\\code\\project\\OurChat2\\Client\\IconSetting", "-IG:\\code\\project\\OurChat2\\Client\\Tools", "-IG:\\code\\project\\OurChat2\\Client\\Rewriting", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtOpenGLWidgets", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtOpenGL", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtWidgets", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtGui", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtNetwork", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtCore", "-IG:\\code\\project\\OurChat\\Client\\build\\Desktop_Qt_6_5_3_MSVC2019_64bit-Debug\\debug", "-IG:\\code\\project\\OurChat\\Client\\build\\Desktop_Qt_6_5_3_MSVC2019_64bit-Debug", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:G:\\software\\Qt\\Qt6.5.3\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\17\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\Community\\VC\\Tools\\MSVC\\14.29.30133\\ATLMFC\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\Community\\VC\\Tools\\MSVC\\14.29.30133\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\NETFXSDK\\4.8\\include\\um", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\ucrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\shared", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\um", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\winrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\cppwinrt", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/TP", "G:\\code\\project\\OurChat2\\Client\\Tools\\tcpclient.h"], "directory": "G:/code/project/OurChat/Client/build/Desktop_Qt_6_5_3_MSVC2019_64bit-Debug/.qtc_clangd", "file": "G:/code/project/OurChat2/Client/Tools/tcpclient.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-nologo", "-Zc:wchar_t", "-FS", "-Zc:rvalueCast", "-Zc:inline", "-Zc:strictStrings", "-Zc:<PERSON><PERSON><PERSON>", "-permissive-", "-Zc:__cplusplus", "-Zc:externConstexpr", "-<PERSON>i", "-MDd", "-clang:-std=c++17", "-utf-8", "-W3", "-w34100", "-w34189", "-w44996", "-w44456", "-w44457", "-w44458", "-wd4577", "-wd4467", "-EHsc", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "-fms-compatibility-version=19.29", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-DWIN64", "-DRC_ICONS", "-DQT_DEPRECATED_WARNINGS", "-DQT_QML_DEBUG", "-DQT_OPENGLWIDGETS_LIB", "-DQT_OPENGL_LIB", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IG:\\software\\Qt\\Qt6.5.3\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IG:\\software\\Qt\\Qt6.5.3\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IG:\\code\\project\\OurChat2\\Client", "-IG:\\code\\project\\OurChat2\\Client\\Adding", "-IG:\\code\\project\\OurChat2\\Client\\Chatting", "-IG:\\code\\project\\OurChat2\\Client\\IconSetting", "-IG:\\code\\project\\OurChat2\\Client\\Tools", "-IG:\\code\\project\\OurChat2\\Client\\Rewriting", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtOpenGLWidgets", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtOpenGL", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtWidgets", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtGui", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtNetwork", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtCore", "-IG:\\code\\project\\OurChat\\Client\\build\\Desktop_Qt_6_5_3_MSVC2019_64bit-Debug\\debug", "-IG:\\code\\project\\OurChat\\Client\\build\\Desktop_Qt_6_5_3_MSVC2019_64bit-Debug", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:G:\\software\\Qt\\Qt6.5.3\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\17\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\Community\\VC\\Tools\\MSVC\\14.29.30133\\ATLMFC\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\Community\\VC\\Tools\\MSVC\\14.29.30133\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\NETFXSDK\\4.8\\include\\um", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\ucrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\shared", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\um", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\winrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\cppwinrt", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/TP", "G:\\code\\project\\OurChat2\\Client\\client.h"], "directory": "G:/code/project/OurChat/Client/build/Desktop_Qt_6_5_3_MSVC2019_64bit-Debug/.qtc_clangd", "file": "G:/code/project/OurChat2/Client/client.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-nologo", "-Zc:wchar_t", "-FS", "-Zc:rvalueCast", "-Zc:inline", "-Zc:strictStrings", "-Zc:<PERSON><PERSON><PERSON>", "-permissive-", "-Zc:__cplusplus", "-Zc:externConstexpr", "-<PERSON>i", "-MDd", "-clang:-std=c++17", "-utf-8", "-W3", "-w34100", "-w34189", "-w44996", "-w44456", "-w44457", "-w44458", "-wd4577", "-wd4467", "-EHsc", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "-fms-compatibility-version=19.29", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-DWIN64", "-DRC_ICONS", "-DQT_DEPRECATED_WARNINGS", "-DQT_QML_DEBUG", "-DQT_OPENGLWIDGETS_LIB", "-DQT_OPENGL_LIB", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IG:\\software\\Qt\\Qt6.5.3\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IG:\\software\\Qt\\Qt6.5.3\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IG:\\code\\project\\OurChat2\\Client", "-IG:\\code\\project\\OurChat2\\Client\\Adding", "-IG:\\code\\project\\OurChat2\\Client\\Chatting", "-IG:\\code\\project\\OurChat2\\Client\\IconSetting", "-IG:\\code\\project\\OurChat2\\Client\\Tools", "-IG:\\code\\project\\OurChat2\\Client\\Rewriting", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtOpenGLWidgets", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtOpenGL", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtWidgets", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtGui", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtNetwork", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtCore", "-IG:\\code\\project\\OurChat\\Client\\build\\Desktop_Qt_6_5_3_MSVC2019_64bit-Debug\\debug", "-IG:\\code\\project\\OurChat\\Client\\build\\Desktop_Qt_6_5_3_MSVC2019_64bit-Debug", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:G:\\software\\Qt\\Qt6.5.3\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\17\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\Community\\VC\\Tools\\MSVC\\14.29.30133\\ATLMFC\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\Community\\VC\\Tools\\MSVC\\14.29.30133\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\NETFXSDK\\4.8\\include\\um", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\ucrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\shared", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\um", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\winrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\cppwinrt", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/TP", "G:\\code\\project\\OurChat2\\Client\\friendinfowidget.h"], "directory": "G:/code/project/OurChat/Client/build/Desktop_Qt_6_5_3_MSVC2019_64bit-Debug/.qtc_clangd", "file": "G:/code/project/OurChat2/Client/friendinfowidget.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-nologo", "-Zc:wchar_t", "-FS", "-Zc:rvalueCast", "-Zc:inline", "-Zc:strictStrings", "-Zc:<PERSON><PERSON><PERSON>", "-permissive-", "-Zc:__cplusplus", "-Zc:externConstexpr", "-<PERSON>i", "-MDd", "-clang:-std=c++17", "-utf-8", "-W3", "-w34100", "-w34189", "-w44996", "-w44456", "-w44457", "-w44458", "-wd4577", "-wd4467", "-EHsc", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "-fms-compatibility-version=19.29", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-DWIN64", "-DRC_ICONS", "-DQT_DEPRECATED_WARNINGS", "-DQT_QML_DEBUG", "-DQT_OPENGLWIDGETS_LIB", "-DQT_OPENGL_LIB", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IG:\\software\\Qt\\Qt6.5.3\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IG:\\software\\Qt\\Qt6.5.3\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IG:\\code\\project\\OurChat2\\Client", "-IG:\\code\\project\\OurChat2\\Client\\Adding", "-IG:\\code\\project\\OurChat2\\Client\\Chatting", "-IG:\\code\\project\\OurChat2\\Client\\IconSetting", "-IG:\\code\\project\\OurChat2\\Client\\Tools", "-IG:\\code\\project\\OurChat2\\Client\\Rewriting", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtOpenGLWidgets", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtOpenGL", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtWidgets", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtGui", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtNetwork", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtCore", "-IG:\\code\\project\\OurChat\\Client\\build\\Desktop_Qt_6_5_3_MSVC2019_64bit-Debug\\debug", "-IG:\\code\\project\\OurChat\\Client\\build\\Desktop_Qt_6_5_3_MSVC2019_64bit-Debug", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:G:\\software\\Qt\\Qt6.5.3\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\17\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\Community\\VC\\Tools\\MSVC\\14.29.30133\\ATLMFC\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\Community\\VC\\Tools\\MSVC\\14.29.30133\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\NETFXSDK\\4.8\\include\\um", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\ucrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\shared", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\um", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\winrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\cppwinrt", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/TP", "G:\\code\\project\\OurChat2\\Client\\logging.h"], "directory": "G:/code/project/OurChat/Client/build/Desktop_Qt_6_5_3_MSVC2019_64bit-Debug/.qtc_clangd", "file": "G:/code/project/OurChat2/Client/logging.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-nologo", "-Zc:wchar_t", "-FS", "-Zc:rvalueCast", "-Zc:inline", "-Zc:strictStrings", "-Zc:<PERSON><PERSON><PERSON>", "-permissive-", "-Zc:__cplusplus", "-Zc:externConstexpr", "-<PERSON>i", "-MDd", "-clang:-std=c++17", "-utf-8", "-W3", "-w34100", "-w34189", "-w44996", "-w44456", "-w44457", "-w44458", "-wd4577", "-wd4467", "-EHsc", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "-fms-compatibility-version=19.29", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-DWIN64", "-DRC_ICONS", "-DQT_DEPRECATED_WARNINGS", "-DQT_QML_DEBUG", "-DQT_OPENGLWIDGETS_LIB", "-DQT_OPENGL_LIB", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IG:\\software\\Qt\\Qt6.5.3\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IG:\\software\\Qt\\Qt6.5.3\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IG:\\code\\project\\OurChat2\\Client", "-IG:\\code\\project\\OurChat2\\Client\\Adding", "-IG:\\code\\project\\OurChat2\\Client\\Chatting", "-IG:\\code\\project\\OurChat2\\Client\\IconSetting", "-IG:\\code\\project\\OurChat2\\Client\\Tools", "-IG:\\code\\project\\OurChat2\\Client\\Rewriting", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtOpenGLWidgets", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtOpenGL", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtWidgets", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtGui", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtNetwork", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtCore", "-IG:\\code\\project\\OurChat\\Client\\build\\Desktop_Qt_6_5_3_MSVC2019_64bit-Debug\\debug", "-IG:\\code\\project\\OurChat\\Client\\build\\Desktop_Qt_6_5_3_MSVC2019_64bit-Debug", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:G:\\software\\Qt\\Qt6.5.3\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\17\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\Community\\VC\\Tools\\MSVC\\14.29.30133\\ATLMFC\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\Community\\VC\\Tools\\MSVC\\14.29.30133\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\NETFXSDK\\4.8\\include\\um", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\ucrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\shared", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\um", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\winrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\cppwinrt", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/TP", "G:\\code\\project\\OurChat2\\Client\\selfinfowidget.h"], "directory": "G:/code/project/OurChat/Client/build/Desktop_Qt_6_5_3_MSVC2019_64bit-Debug/.qtc_clangd", "file": "G:/code/project/OurChat2/Client/selfinfowidget.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-nologo", "-Zc:wchar_t", "-FS", "-Zc:rvalueCast", "-Zc:inline", "-Zc:strictStrings", "-Zc:<PERSON><PERSON><PERSON>", "-permissive-", "-Zc:__cplusplus", "-Zc:externConstexpr", "-<PERSON>i", "-MDd", "-clang:-std=c++17", "-utf-8", "-W3", "-w34100", "-w34189", "-w44996", "-w44456", "-w44457", "-w44458", "-wd4577", "-wd4467", "-EHsc", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "-fms-compatibility-version=19.29", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-DWIN64", "-DRC_ICONS", "-DQT_DEPRECATED_WARNINGS", "-DQT_QML_DEBUG", "-DQT_OPENGLWIDGETS_LIB", "-DQT_OPENGL_LIB", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IG:\\software\\Qt\\Qt6.5.3\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IG:\\software\\Qt\\Qt6.5.3\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IG:\\code\\project\\OurChat2\\Client", "-IG:\\code\\project\\OurChat2\\Client\\Adding", "-IG:\\code\\project\\OurChat2\\Client\\Chatting", "-IG:\\code\\project\\OurChat2\\Client\\IconSetting", "-IG:\\code\\project\\OurChat2\\Client\\Tools", "-IG:\\code\\project\\OurChat2\\Client\\Rewriting", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtOpenGLWidgets", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtOpenGL", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtWidgets", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtGui", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtNetwork", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtCore", "-IG:\\code\\project\\OurChat\\Client\\build\\Desktop_Qt_6_5_3_MSVC2019_64bit-Debug\\debug", "-IG:\\code\\project\\OurChat\\Client\\build\\Desktop_Qt_6_5_3_MSVC2019_64bit-Debug", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:G:\\software\\Qt\\Qt6.5.3\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\17\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\Community\\VC\\Tools\\MSVC\\14.29.30133\\ATLMFC\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\Community\\VC\\Tools\\MSVC\\14.29.30133\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\NETFXSDK\\4.8\\include\\um", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\ucrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\shared", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\um", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\winrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\cppwinrt", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/TP", "G:\\code\\project\\OurChat\\Client\\build\\Desktop_Qt_6_5_3_MSVC2019_64bit-Debug\\ui_iconselect.h"], "directory": "G:/code/project/OurChat/Client/build/Desktop_Qt_6_5_3_MSVC2019_64bit-Debug/.qtc_clangd", "file": "G:/code/project/OurChat/Client/build/Desktop_Qt_6_5_3_MSVC2019_64bit-Debug/ui_iconselect.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-nologo", "-Zc:wchar_t", "-FS", "-Zc:rvalueCast", "-Zc:inline", "-Zc:strictStrings", "-Zc:<PERSON><PERSON><PERSON>", "-permissive-", "-Zc:__cplusplus", "-Zc:externConstexpr", "-<PERSON>i", "-MDd", "-clang:-std=c++17", "-utf-8", "-W3", "-w34100", "-w34189", "-w44996", "-w44456", "-w44457", "-w44458", "-wd4577", "-wd4467", "-EHsc", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "-fms-compatibility-version=19.29", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-DWIN64", "-DRC_ICONS", "-DQT_DEPRECATED_WARNINGS", "-DQT_QML_DEBUG", "-DQT_OPENGLWIDGETS_LIB", "-DQT_OPENGL_LIB", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IG:\\software\\Qt\\Qt6.5.3\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IG:\\software\\Qt\\Qt6.5.3\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IG:\\code\\project\\OurChat2\\Client", "-IG:\\code\\project\\OurChat2\\Client\\Adding", "-IG:\\code\\project\\OurChat2\\Client\\Chatting", "-IG:\\code\\project\\OurChat2\\Client\\IconSetting", "-IG:\\code\\project\\OurChat2\\Client\\Tools", "-IG:\\code\\project\\OurChat2\\Client\\Rewriting", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtOpenGLWidgets", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtOpenGL", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtWidgets", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtGui", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtNetwork", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtCore", "-IG:\\code\\project\\OurChat\\Client\\build\\Desktop_Qt_6_5_3_MSVC2019_64bit-Debug\\debug", "-IG:\\code\\project\\OurChat\\Client\\build\\Desktop_Qt_6_5_3_MSVC2019_64bit-Debug", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:G:\\software\\Qt\\Qt6.5.3\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\17\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\Community\\VC\\Tools\\MSVC\\14.29.30133\\ATLMFC\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\Community\\VC\\Tools\\MSVC\\14.29.30133\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\NETFXSDK\\4.8\\include\\um", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\ucrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\shared", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\um", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\winrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\cppwinrt", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/TP", "G:\\code\\project\\OurChat\\Client\\build\\Desktop_Qt_6_5_3_MSVC2019_64bit-Debug\\ui_chatwindow.h"], "directory": "G:/code/project/OurChat/Client/build/Desktop_Qt_6_5_3_MSVC2019_64bit-Debug/.qtc_clangd", "file": "G:/code/project/OurChat/Client/build/Desktop_Qt_6_5_3_MSVC2019_64bit-Debug/ui_chatwindow.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-nologo", "-Zc:wchar_t", "-FS", "-Zc:rvalueCast", "-Zc:inline", "-Zc:strictStrings", "-Zc:<PERSON><PERSON><PERSON>", "-permissive-", "-Zc:__cplusplus", "-Zc:externConstexpr", "-<PERSON>i", "-MDd", "-clang:-std=c++17", "-utf-8", "-W3", "-w34100", "-w34189", "-w44996", "-w44456", "-w44457", "-w44458", "-wd4577", "-wd4467", "-EHsc", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "-fms-compatibility-version=19.29", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-DWIN64", "-DRC_ICONS", "-DQT_DEPRECATED_WARNINGS", "-DQT_QML_DEBUG", "-DQT_OPENGLWIDGETS_LIB", "-DQT_OPENGL_LIB", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IG:\\software\\Qt\\Qt6.5.3\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IG:\\software\\Qt\\Qt6.5.3\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IG:\\code\\project\\OurChat2\\Client", "-IG:\\code\\project\\OurChat2\\Client\\Adding", "-IG:\\code\\project\\OurChat2\\Client\\Chatting", "-IG:\\code\\project\\OurChat2\\Client\\IconSetting", "-IG:\\code\\project\\OurChat2\\Client\\Tools", "-IG:\\code\\project\\OurChat2\\Client\\Rewriting", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtOpenGLWidgets", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtOpenGL", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtWidgets", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtGui", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtNetwork", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtCore", "-IG:\\code\\project\\OurChat\\Client\\build\\Desktop_Qt_6_5_3_MSVC2019_64bit-Debug\\debug", "-IG:\\code\\project\\OurChat\\Client\\build\\Desktop_Qt_6_5_3_MSVC2019_64bit-Debug", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:G:\\software\\Qt\\Qt6.5.3\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\17\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\Community\\VC\\Tools\\MSVC\\14.29.30133\\ATLMFC\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\Community\\VC\\Tools\\MSVC\\14.29.30133\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\NETFXSDK\\4.8\\include\\um", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\ucrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\shared", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\um", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\winrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\cppwinrt", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/TP", "G:\\code\\project\\OurChat\\Client\\build\\Desktop_Qt_6_5_3_MSVC2019_64bit-Debug\\ui_addfriend.h"], "directory": "G:/code/project/OurChat/Client/build/Desktop_Qt_6_5_3_MSVC2019_64bit-Debug/.qtc_clangd", "file": "G:/code/project/OurChat/Client/build/Desktop_Qt_6_5_3_MSVC2019_64bit-Debug/ui_addfriend.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-nologo", "-Zc:wchar_t", "-FS", "-Zc:rvalueCast", "-Zc:inline", "-Zc:strictStrings", "-Zc:<PERSON><PERSON><PERSON>", "-permissive-", "-Zc:__cplusplus", "-Zc:externConstexpr", "-<PERSON>i", "-MDd", "-clang:-std=c++17", "-utf-8", "-W3", "-w34100", "-w34189", "-w44996", "-w44456", "-w44457", "-w44458", "-wd4577", "-wd4467", "-EHsc", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "-fms-compatibility-version=19.29", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-DWIN64", "-DRC_ICONS", "-DQT_DEPRECATED_WARNINGS", "-DQT_QML_DEBUG", "-DQT_OPENGLWIDGETS_LIB", "-DQT_OPENGL_LIB", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IG:\\software\\Qt\\Qt6.5.3\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IG:\\software\\Qt\\Qt6.5.3\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IG:\\code\\project\\OurChat2\\Client", "-IG:\\code\\project\\OurChat2\\Client\\Adding", "-IG:\\code\\project\\OurChat2\\Client\\Chatting", "-IG:\\code\\project\\OurChat2\\Client\\IconSetting", "-IG:\\code\\project\\OurChat2\\Client\\Tools", "-IG:\\code\\project\\OurChat2\\Client\\Rewriting", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtOpenGLWidgets", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtOpenGL", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtWidgets", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtGui", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtNetwork", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtCore", "-IG:\\code\\project\\OurChat\\Client\\build\\Desktop_Qt_6_5_3_MSVC2019_64bit-Debug\\debug", "-IG:\\code\\project\\OurChat\\Client\\build\\Desktop_Qt_6_5_3_MSVC2019_64bit-Debug", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:G:\\software\\Qt\\Qt6.5.3\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\17\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\Community\\VC\\Tools\\MSVC\\14.29.30133\\ATLMFC\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\Community\\VC\\Tools\\MSVC\\14.29.30133\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\NETFXSDK\\4.8\\include\\um", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\ucrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\shared", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\um", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\winrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\cppwinrt", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/TP", "G:\\code\\project\\OurChat\\Client\\build\\Desktop_Qt_6_5_3_MSVC2019_64bit-Debug\\ui_selfinfowidget.h"], "directory": "G:/code/project/OurChat/Client/build/Desktop_Qt_6_5_3_MSVC2019_64bit-Debug/.qtc_clangd", "file": "G:/code/project/OurChat/Client/build/Desktop_Qt_6_5_3_MSVC2019_64bit-Debug/ui_selfinfowidget.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-nologo", "-Zc:wchar_t", "-FS", "-Zc:rvalueCast", "-Zc:inline", "-Zc:strictStrings", "-Zc:<PERSON><PERSON><PERSON>", "-permissive-", "-Zc:__cplusplus", "-Zc:externConstexpr", "-<PERSON>i", "-MDd", "-clang:-std=c++17", "-utf-8", "-W3", "-w34100", "-w34189", "-w44996", "-w44456", "-w44457", "-w44458", "-wd4577", "-wd4467", "-EHsc", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "-fms-compatibility-version=19.29", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-DWIN64", "-DRC_ICONS", "-DQT_DEPRECATED_WARNINGS", "-DQT_QML_DEBUG", "-DQT_OPENGLWIDGETS_LIB", "-DQT_OPENGL_LIB", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IG:\\software\\Qt\\Qt6.5.3\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IG:\\software\\Qt\\Qt6.5.3\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IG:\\code\\project\\OurChat2\\Client", "-IG:\\code\\project\\OurChat2\\Client\\Adding", "-IG:\\code\\project\\OurChat2\\Client\\Chatting", "-IG:\\code\\project\\OurChat2\\Client\\IconSetting", "-IG:\\code\\project\\OurChat2\\Client\\Tools", "-IG:\\code\\project\\OurChat2\\Client\\Rewriting", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtOpenGLWidgets", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtOpenGL", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtWidgets", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtGui", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtNetwork", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtCore", "-IG:\\code\\project\\OurChat\\Client\\build\\Desktop_Qt_6_5_3_MSVC2019_64bit-Debug\\debug", "-IG:\\code\\project\\OurChat\\Client\\build\\Desktop_Qt_6_5_3_MSVC2019_64bit-Debug", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:G:\\software\\Qt\\Qt6.5.3\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\17\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\Community\\VC\\Tools\\MSVC\\14.29.30133\\ATLMFC\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\Community\\VC\\Tools\\MSVC\\14.29.30133\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\NETFXSDK\\4.8\\include\\um", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\ucrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\shared", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\um", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\winrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\cppwinrt", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/TP", "G:\\code\\project\\OurChat\\Client\\build\\Desktop_Qt_6_5_3_MSVC2019_64bit-Debug\\ui_client.h"], "directory": "G:/code/project/OurChat/Client/build/Desktop_Qt_6_5_3_MSVC2019_64bit-Debug/.qtc_clangd", "file": "G:/code/project/OurChat/Client/build/Desktop_Qt_6_5_3_MSVC2019_64bit-Debug/ui_client.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-nologo", "-Zc:wchar_t", "-FS", "-Zc:rvalueCast", "-Zc:inline", "-Zc:strictStrings", "-Zc:<PERSON><PERSON><PERSON>", "-permissive-", "-Zc:__cplusplus", "-Zc:externConstexpr", "-<PERSON>i", "-MDd", "-clang:-std=c++17", "-utf-8", "-W3", "-w34100", "-w34189", "-w44996", "-w44456", "-w44457", "-w44458", "-wd4577", "-wd4467", "-EHsc", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "-fms-compatibility-version=19.29", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-DWIN64", "-DRC_ICONS", "-DQT_DEPRECATED_WARNINGS", "-DQT_QML_DEBUG", "-DQT_OPENGLWIDGETS_LIB", "-DQT_OPENGL_LIB", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IG:\\software\\Qt\\Qt6.5.3\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IG:\\software\\Qt\\Qt6.5.3\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IG:\\code\\project\\OurChat2\\Client", "-IG:\\code\\project\\OurChat2\\Client\\Adding", "-IG:\\code\\project\\OurChat2\\Client\\Chatting", "-IG:\\code\\project\\OurChat2\\Client\\IconSetting", "-IG:\\code\\project\\OurChat2\\Client\\Tools", "-IG:\\code\\project\\OurChat2\\Client\\Rewriting", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtOpenGLWidgets", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtOpenGL", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtWidgets", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtGui", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtNetwork", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtCore", "-IG:\\code\\project\\OurChat\\Client\\build\\Desktop_Qt_6_5_3_MSVC2019_64bit-Debug\\debug", "-IG:\\code\\project\\OurChat\\Client\\build\\Desktop_Qt_6_5_3_MSVC2019_64bit-Debug", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:G:\\software\\Qt\\Qt6.5.3\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\17\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\Community\\VC\\Tools\\MSVC\\14.29.30133\\ATLMFC\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\Community\\VC\\Tools\\MSVC\\14.29.30133\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\NETFXSDK\\4.8\\include\\um", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\ucrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\shared", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\um", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\winrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\cppwinrt", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/TP", "G:\\code\\project\\OurChat\\Client\\build\\Desktop_Qt_6_5_3_MSVC2019_64bit-Debug\\ui_friendinfowidget.h"], "directory": "G:/code/project/OurChat/Client/build/Desktop_Qt_6_5_3_MSVC2019_64bit-Debug/.qtc_clangd", "file": "G:/code/project/OurChat/Client/build/Desktop_Qt_6_5_3_MSVC2019_64bit-Debug/ui_friendinfowidget.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-nologo", "-Zc:wchar_t", "-FS", "-Zc:rvalueCast", "-Zc:inline", "-Zc:strictStrings", "-Zc:<PERSON><PERSON><PERSON>", "-permissive-", "-Zc:__cplusplus", "-Zc:externConstexpr", "-<PERSON>i", "-MDd", "-clang:-std=c++17", "-utf-8", "-W3", "-w34100", "-w34189", "-w44996", "-w44456", "-w44457", "-w44458", "-wd4577", "-wd4467", "-EHsc", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "-fms-compatibility-version=19.29", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-DWIN64", "-DRC_ICONS", "-DQT_DEPRECATED_WARNINGS", "-DQT_QML_DEBUG", "-DQT_OPENGLWIDGETS_LIB", "-DQT_OPENGL_LIB", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IG:\\software\\Qt\\Qt6.5.3\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IG:\\software\\Qt\\Qt6.5.3\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IG:\\code\\project\\OurChat2\\Client", "-IG:\\code\\project\\OurChat2\\Client\\Adding", "-IG:\\code\\project\\OurChat2\\Client\\Chatting", "-IG:\\code\\project\\OurChat2\\Client\\IconSetting", "-IG:\\code\\project\\OurChat2\\Client\\Tools", "-IG:\\code\\project\\OurChat2\\Client\\Rewriting", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtOpenGLWidgets", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtOpenGL", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtWidgets", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtGui", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtNetwork", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtCore", "-IG:\\code\\project\\OurChat\\Client\\build\\Desktop_Qt_6_5_3_MSVC2019_64bit-Debug\\debug", "-IG:\\code\\project\\OurChat\\Client\\build\\Desktop_Qt_6_5_3_MSVC2019_64bit-Debug", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:G:\\software\\Qt\\Qt6.5.3\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\17\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\Community\\VC\\Tools\\MSVC\\14.29.30133\\ATLMFC\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\Community\\VC\\Tools\\MSVC\\14.29.30133\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\NETFXSDK\\4.8\\include\\um", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\ucrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\shared", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\um", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\winrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\cppwinrt", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/TP", "G:\\code\\project\\OurChat\\Client\\build\\Desktop_Qt_6_5_3_MSVC2019_64bit-Debug\\ui_logging.h"], "directory": "G:/code/project/OurChat/Client/build/Desktop_Qt_6_5_3_MSVC2019_64bit-Debug/.qtc_clangd", "file": "G:/code/project/OurChat/Client/build/Desktop_Qt_6_5_3_MSVC2019_64bit-Debug/ui_logging.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-nologo", "-Zc:wchar_t", "-FS", "-Zc:rvalueCast", "-Zc:inline", "-Zc:strictStrings", "-Zc:<PERSON><PERSON><PERSON>", "-permissive-", "-Zc:__cplusplus", "-Zc:externConstexpr", "-<PERSON>i", "-MDd", "-clang:-std=c++17", "-utf-8", "-W3", "-w34100", "-w34189", "-w44996", "-w44456", "-w44457", "-w44458", "-wd4577", "-wd4467", "-EHsc", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "-fms-compatibility-version=19.29", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-DWIN64", "-DRC_ICONS", "-DQT_DEPRECATED_WARNINGS", "-DQT_QML_DEBUG", "-DQT_OPENGLWIDGETS_LIB", "-DQT_OPENGL_LIB", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IG:\\software\\Qt\\Qt6.5.3\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IG:\\software\\Qt\\Qt6.5.3\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IG:\\code\\project\\OurChat2\\Client", "-IG:\\code\\project\\OurChat2\\Client\\Adding", "-IG:\\code\\project\\OurChat2\\Client\\Chatting", "-IG:\\code\\project\\OurChat2\\Client\\IconSetting", "-IG:\\code\\project\\OurChat2\\Client\\Tools", "-IG:\\code\\project\\OurChat2\\Client\\Rewriting", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtOpenGLWidgets", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtOpenGL", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtWidgets", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtGui", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtNetwork", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtCore", "-IG:\\code\\project\\OurChat\\Client\\build\\Desktop_Qt_6_5_3_MSVC2019_64bit-Debug\\debug", "-IG:\\code\\project\\OurChat\\Client\\build\\Desktop_Qt_6_5_3_MSVC2019_64bit-Debug", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:G:\\software\\Qt\\Qt6.5.3\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\17\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\Community\\VC\\Tools\\MSVC\\14.29.30133\\ATLMFC\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\Community\\VC\\Tools\\MSVC\\14.29.30133\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\NETFXSDK\\4.8\\include\\um", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\ucrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\shared", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\um", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\winrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\cppwinrt", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/TP", "G:\\code\\project\\OurChat\\Client\\build\\Desktop_Qt_6_5_3_MSVC2019_64bit-Debug\\ui_systemmessage.h"], "directory": "G:/code/project/OurChat/Client/build/Desktop_Qt_6_5_3_MSVC2019_64bit-Debug/.qtc_clangd", "file": "G:/code/project/OurChat/Client/build/Desktop_Qt_6_5_3_MSVC2019_64bit-Debug/ui_systemmessage.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-nologo", "-Zc:wchar_t", "-FS", "-Zc:rvalueCast", "-Zc:inline", "-Zc:strictStrings", "-Zc:<PERSON><PERSON><PERSON>", "-permissive-", "-Zc:__cplusplus", "-Zc:externConstexpr", "-<PERSON>i", "-MDd", "-clang:-std=c++17", "-utf-8", "-W3", "-w34100", "-w34189", "-w44996", "-w44456", "-w44457", "-w44458", "-wd4577", "-wd4467", "-EHsc", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "-fms-compatibility-version=19.29", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-DWIN64", "-DRC_ICONS", "-DQT_DEPRECATED_WARNINGS", "-DQT_QML_DEBUG", "-DQT_OPENGLWIDGETS_LIB", "-DQT_OPENGL_LIB", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IG:\\software\\Qt\\Qt6.5.3\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IG:\\software\\Qt\\Qt6.5.3\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IG:\\code\\project\\OurChat2\\Client", "-IG:\\code\\project\\OurChat2\\Client\\Adding", "-IG:\\code\\project\\OurChat2\\Client\\Chatting", "-IG:\\code\\project\\OurChat2\\Client\\IconSetting", "-IG:\\code\\project\\OurChat2\\Client\\Tools", "-IG:\\code\\project\\OurChat2\\Client\\Rewriting", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtOpenGLWidgets", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtOpenGL", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtWidgets", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtGui", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtNetwork", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtCore", "-IG:\\code\\project\\OurChat\\Client\\build\\Desktop_Qt_6_5_3_MSVC2019_64bit-Debug\\debug", "-IG:\\code\\project\\OurChat\\Client\\build\\Desktop_Qt_6_5_3_MSVC2019_64bit-Debug", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:G:\\software\\Qt\\Qt6.5.3\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\17\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\Community\\VC\\Tools\\MSVC\\14.29.30133\\ATLMFC\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\Community\\VC\\Tools\\MSVC\\14.29.30133\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\NETFXSDK\\4.8\\include\\um", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\ucrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\shared", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\um", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\winrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\cppwinrt", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/TP", "G:\\code\\project\\OurChat\\Client\\build\\Desktop_Qt_6_5_3_MSVC2019_64bit-Debug\\ui_verificationitem.h"], "directory": "G:/code/project/OurChat/Client/build/Desktop_Qt_6_5_3_MSVC2019_64bit-Debug/.qtc_clangd", "file": "G:/code/project/OurChat/Client/build/Desktop_Qt_6_5_3_MSVC2019_64bit-Debug/ui_verificationitem.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-nologo", "-Zc:wchar_t", "-FS", "-Zc:rvalueCast", "-Zc:inline", "-Zc:strictStrings", "-Zc:<PERSON><PERSON><PERSON>", "-permissive-", "-Zc:__cplusplus", "-Zc:externConstexpr", "-<PERSON>i", "-MDd", "-clang:-std=c++17", "-utf-8", "-W3", "-w34100", "-w34189", "-w44996", "-w44456", "-w44457", "-w44458", "-wd4577", "-wd4467", "-EHsc", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "-fms-compatibility-version=19.29", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-DWIN64", "-DRC_ICONS", "-DQT_DEPRECATED_WARNINGS", "-DQT_QML_DEBUG", "-DQT_OPENGLWIDGETS_LIB", "-DQT_OPENGL_LIB", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IG:\\software\\Qt\\Qt6.5.3\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IG:\\software\\Qt\\Qt6.5.3\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IG:\\code\\project\\OurChat2\\Client", "-IG:\\code\\project\\OurChat2\\Client\\Adding", "-IG:\\code\\project\\OurChat2\\Client\\Chatting", "-IG:\\code\\project\\OurChat2\\Client\\IconSetting", "-IG:\\code\\project\\OurChat2\\Client\\Tools", "-IG:\\code\\project\\OurChat2\\Client\\Rewriting", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtOpenGLWidgets", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtOpenGL", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtWidgets", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtGui", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtNetwork", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtCore", "-IG:\\code\\project\\OurChat\\Client\\build\\Desktop_Qt_6_5_3_MSVC2019_64bit-Debug\\debug", "-IG:\\code\\project\\OurChat\\Client\\build\\Desktop_Qt_6_5_3_MSVC2019_64bit-Debug", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:G:\\software\\Qt\\Qt6.5.3\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\17\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\Community\\VC\\Tools\\MSVC\\14.29.30133\\ATLMFC\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\Community\\VC\\Tools\\MSVC\\14.29.30133\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\NETFXSDK\\4.8\\include\\um", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\ucrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\shared", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\um", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\winrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\cppwinrt", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/TP", "G:\\code\\project\\OurChat\\Client\\build\\Desktop_Qt_6_5_3_MSVC2019_64bit-Debug\\ui_imagepreview.h"], "directory": "G:/code/project/OurChat/Client/build/Desktop_Qt_6_5_3_MSVC2019_64bit-Debug/.qtc_clangd", "file": "G:/code/project/OurChat/Client/build/Desktop_Qt_6_5_3_MSVC2019_64bit-Debug/ui_imagepreview.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-nologo", "-Zc:wchar_t", "-FS", "-Zc:rvalueCast", "-Zc:inline", "-Zc:strictStrings", "-Zc:<PERSON><PERSON><PERSON>", "-permissive-", "-Zc:__cplusplus", "-Zc:externConstexpr", "-<PERSON>i", "-MDd", "-clang:-std=c++17", "-utf-8", "-W3", "-w34100", "-w34189", "-w44996", "-w44456", "-w44457", "-w44458", "-wd4577", "-wd4467", "-EHsc", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "-fms-compatibility-version=19.29", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-DWIN64", "-DRC_ICONS", "-DQT_DEPRECATED_WARNINGS", "-DQT_QML_DEBUG", "-DQT_OPENGLWIDGETS_LIB", "-DQT_OPENGL_LIB", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IG:\\software\\Qt\\Qt6.5.3\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IG:\\software\\Qt\\Qt6.5.3\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IG:\\code\\project\\OurChat2\\Client", "-IG:\\code\\project\\OurChat2\\Client\\Adding", "-IG:\\code\\project\\OurChat2\\Client\\Chatting", "-IG:\\code\\project\\OurChat2\\Client\\IconSetting", "-IG:\\code\\project\\OurChat2\\Client\\Tools", "-IG:\\code\\project\\OurChat2\\Client\\Rewriting", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtOpenGLWidgets", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtOpenGL", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtWidgets", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtGui", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtNetwork", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\include\\QtCore", "-IG:\\code\\project\\OurChat\\Client\\build\\Desktop_Qt_6_5_3_MSVC2019_64bit-Debug\\debug", "-IG:\\code\\project\\OurChat\\Client\\build\\Desktop_Qt_6_5_3_MSVC2019_64bit-Debug", "-IG:\\software\\Qt\\Qt6.5.3\\6.5.3\\msvc2019_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:G:\\software\\Qt\\Qt6.5.3\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\17\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\Community\\VC\\Tools\\MSVC\\14.29.30133\\ATLMFC\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\Community\\VC\\Tools\\MSVC\\14.29.30133\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\NETFXSDK\\4.8\\include\\um", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\ucrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\shared", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\um", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\winrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\cppwinrt", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/TP", "G:\\code\\project\\OurChat\\Client\\build\\Desktop_Qt_6_5_3_MSVC2019_64bit-Debug\\ui_frienditem.h"], "directory": "G:/code/project/OurChat/Client/build/Desktop_Qt_6_5_3_MSVC2019_64bit-Debug/.qtc_clangd", "file": "G:/code/project/OurChat/Client/build/Desktop_Qt_6_5_3_MSVC2019_64bit-Debug/ui_frienditem.h"}]