# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.20

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/local/bin/cmake

# The command to remove a file.
RM = /usr/local/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/OurChat/server

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/OurChat/server/build

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target install/strip
install/strip: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing the project stripped..."
	/usr/local/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip

# Special rule for the target install/strip
install/strip/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing the project stripped..."
	/usr/local/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip/fast

# Special rule for the target install/local
install/local: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing only the local directory..."
	/usr/local/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local

# Special rule for the target install/local
install/local/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing only the local directory..."
	/usr/local/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running CMake to regenerate build system..."
	/usr/local/bin/cmake --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# Special rule for the target install
install: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Install the project..."
	/usr/local/bin/cmake -P cmake_install.cmake
.PHONY : install

# Special rule for the target install
install/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Install the project..."
	/usr/local/bin/cmake -P cmake_install.cmake
.PHONY : install/fast

# Special rule for the target list_install_components
list_install_components:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Available install components are: \"Unspecified\" \"headers\" \"libraries\""
.PHONY : list_install_components

# Special rule for the target list_install_components
list_install_components/fast: list_install_components
.PHONY : list_install_components/fast

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "No interactive CMake dialog available..."
	/usr/local/bin/cmake -E echo No\ interactive\ CMake\ dialog\ available.
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# The main all target
all: cmake_check_build_system
	cd /home/<USER>/OurChat/server/build && $(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/OurChat/server/build/CMakeFiles /home/<USER>/OurChat/server/build/thirdparty/SQLiteCpp//CMakeFiles/progress.marks
	cd /home/<USER>/OurChat/server/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 thirdparty/SQLiteCpp/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/OurChat/server/build/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	cd /home/<USER>/OurChat/server/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 thirdparty/SQLiteCpp/clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	cd /home/<USER>/OurChat/server/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 thirdparty/SQLiteCpp/preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	cd /home/<USER>/OurChat/server/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 thirdparty/SQLiteCpp/preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	cd /home/<USER>/OurChat/server/build && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

# Convenience name for target.
thirdparty/SQLiteCpp/CMakeFiles/SQLiteCpp_cpplint.dir/rule:
	cd /home/<USER>/OurChat/server/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 thirdparty/SQLiteCpp/CMakeFiles/SQLiteCpp_cpplint.dir/rule
.PHONY : thirdparty/SQLiteCpp/CMakeFiles/SQLiteCpp_cpplint.dir/rule

# Convenience name for target.
SQLiteCpp_cpplint: thirdparty/SQLiteCpp/CMakeFiles/SQLiteCpp_cpplint.dir/rule
.PHONY : SQLiteCpp_cpplint

# fast build rule for target.
SQLiteCpp_cpplint/fast:
	cd /home/<USER>/OurChat/server/build && $(MAKE) $(MAKESILENT) -f thirdparty/SQLiteCpp/CMakeFiles/SQLiteCpp_cpplint.dir/build.make thirdparty/SQLiteCpp/CMakeFiles/SQLiteCpp_cpplint.dir/build
.PHONY : SQLiteCpp_cpplint/fast

# Convenience name for target.
thirdparty/SQLiteCpp/CMakeFiles/SQLiteCpp.dir/rule:
	cd /home/<USER>/OurChat/server/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 thirdparty/SQLiteCpp/CMakeFiles/SQLiteCpp.dir/rule
.PHONY : thirdparty/SQLiteCpp/CMakeFiles/SQLiteCpp.dir/rule

# Convenience name for target.
SQLiteCpp: thirdparty/SQLiteCpp/CMakeFiles/SQLiteCpp.dir/rule
.PHONY : SQLiteCpp

# fast build rule for target.
SQLiteCpp/fast:
	cd /home/<USER>/OurChat/server/build && $(MAKE) $(MAKESILENT) -f thirdparty/SQLiteCpp/CMakeFiles/SQLiteCpp.dir/build.make thirdparty/SQLiteCpp/CMakeFiles/SQLiteCpp.dir/build
.PHONY : SQLiteCpp/fast

src/Backup.o: src/Backup.cpp.o
.PHONY : src/Backup.o

# target to build an object file
src/Backup.cpp.o:
	cd /home/<USER>/OurChat/server/build && $(MAKE) $(MAKESILENT) -f thirdparty/SQLiteCpp/CMakeFiles/SQLiteCpp.dir/build.make thirdparty/SQLiteCpp/CMakeFiles/SQLiteCpp.dir/src/Backup.cpp.o
.PHONY : src/Backup.cpp.o

src/Backup.i: src/Backup.cpp.i
.PHONY : src/Backup.i

# target to preprocess a source file
src/Backup.cpp.i:
	cd /home/<USER>/OurChat/server/build && $(MAKE) $(MAKESILENT) -f thirdparty/SQLiteCpp/CMakeFiles/SQLiteCpp.dir/build.make thirdparty/SQLiteCpp/CMakeFiles/SQLiteCpp.dir/src/Backup.cpp.i
.PHONY : src/Backup.cpp.i

src/Backup.s: src/Backup.cpp.s
.PHONY : src/Backup.s

# target to generate assembly for a file
src/Backup.cpp.s:
	cd /home/<USER>/OurChat/server/build && $(MAKE) $(MAKESILENT) -f thirdparty/SQLiteCpp/CMakeFiles/SQLiteCpp.dir/build.make thirdparty/SQLiteCpp/CMakeFiles/SQLiteCpp.dir/src/Backup.cpp.s
.PHONY : src/Backup.cpp.s

src/Column.o: src/Column.cpp.o
.PHONY : src/Column.o

# target to build an object file
src/Column.cpp.o:
	cd /home/<USER>/OurChat/server/build && $(MAKE) $(MAKESILENT) -f thirdparty/SQLiteCpp/CMakeFiles/SQLiteCpp.dir/build.make thirdparty/SQLiteCpp/CMakeFiles/SQLiteCpp.dir/src/Column.cpp.o
.PHONY : src/Column.cpp.o

src/Column.i: src/Column.cpp.i
.PHONY : src/Column.i

# target to preprocess a source file
src/Column.cpp.i:
	cd /home/<USER>/OurChat/server/build && $(MAKE) $(MAKESILENT) -f thirdparty/SQLiteCpp/CMakeFiles/SQLiteCpp.dir/build.make thirdparty/SQLiteCpp/CMakeFiles/SQLiteCpp.dir/src/Column.cpp.i
.PHONY : src/Column.cpp.i

src/Column.s: src/Column.cpp.s
.PHONY : src/Column.s

# target to generate assembly for a file
src/Column.cpp.s:
	cd /home/<USER>/OurChat/server/build && $(MAKE) $(MAKESILENT) -f thirdparty/SQLiteCpp/CMakeFiles/SQLiteCpp.dir/build.make thirdparty/SQLiteCpp/CMakeFiles/SQLiteCpp.dir/src/Column.cpp.s
.PHONY : src/Column.cpp.s

src/Database.o: src/Database.cpp.o
.PHONY : src/Database.o

# target to build an object file
src/Database.cpp.o:
	cd /home/<USER>/OurChat/server/build && $(MAKE) $(MAKESILENT) -f thirdparty/SQLiteCpp/CMakeFiles/SQLiteCpp.dir/build.make thirdparty/SQLiteCpp/CMakeFiles/SQLiteCpp.dir/src/Database.cpp.o
.PHONY : src/Database.cpp.o

src/Database.i: src/Database.cpp.i
.PHONY : src/Database.i

# target to preprocess a source file
src/Database.cpp.i:
	cd /home/<USER>/OurChat/server/build && $(MAKE) $(MAKESILENT) -f thirdparty/SQLiteCpp/CMakeFiles/SQLiteCpp.dir/build.make thirdparty/SQLiteCpp/CMakeFiles/SQLiteCpp.dir/src/Database.cpp.i
.PHONY : src/Database.cpp.i

src/Database.s: src/Database.cpp.s
.PHONY : src/Database.s

# target to generate assembly for a file
src/Database.cpp.s:
	cd /home/<USER>/OurChat/server/build && $(MAKE) $(MAKESILENT) -f thirdparty/SQLiteCpp/CMakeFiles/SQLiteCpp.dir/build.make thirdparty/SQLiteCpp/CMakeFiles/SQLiteCpp.dir/src/Database.cpp.s
.PHONY : src/Database.cpp.s

src/Exception.o: src/Exception.cpp.o
.PHONY : src/Exception.o

# target to build an object file
src/Exception.cpp.o:
	cd /home/<USER>/OurChat/server/build && $(MAKE) $(MAKESILENT) -f thirdparty/SQLiteCpp/CMakeFiles/SQLiteCpp.dir/build.make thirdparty/SQLiteCpp/CMakeFiles/SQLiteCpp.dir/src/Exception.cpp.o
.PHONY : src/Exception.cpp.o

src/Exception.i: src/Exception.cpp.i
.PHONY : src/Exception.i

# target to preprocess a source file
src/Exception.cpp.i:
	cd /home/<USER>/OurChat/server/build && $(MAKE) $(MAKESILENT) -f thirdparty/SQLiteCpp/CMakeFiles/SQLiteCpp.dir/build.make thirdparty/SQLiteCpp/CMakeFiles/SQLiteCpp.dir/src/Exception.cpp.i
.PHONY : src/Exception.cpp.i

src/Exception.s: src/Exception.cpp.s
.PHONY : src/Exception.s

# target to generate assembly for a file
src/Exception.cpp.s:
	cd /home/<USER>/OurChat/server/build && $(MAKE) $(MAKESILENT) -f thirdparty/SQLiteCpp/CMakeFiles/SQLiteCpp.dir/build.make thirdparty/SQLiteCpp/CMakeFiles/SQLiteCpp.dir/src/Exception.cpp.s
.PHONY : src/Exception.cpp.s

src/Savepoint.o: src/Savepoint.cpp.o
.PHONY : src/Savepoint.o

# target to build an object file
src/Savepoint.cpp.o:
	cd /home/<USER>/OurChat/server/build && $(MAKE) $(MAKESILENT) -f thirdparty/SQLiteCpp/CMakeFiles/SQLiteCpp.dir/build.make thirdparty/SQLiteCpp/CMakeFiles/SQLiteCpp.dir/src/Savepoint.cpp.o
.PHONY : src/Savepoint.cpp.o

src/Savepoint.i: src/Savepoint.cpp.i
.PHONY : src/Savepoint.i

# target to preprocess a source file
src/Savepoint.cpp.i:
	cd /home/<USER>/OurChat/server/build && $(MAKE) $(MAKESILENT) -f thirdparty/SQLiteCpp/CMakeFiles/SQLiteCpp.dir/build.make thirdparty/SQLiteCpp/CMakeFiles/SQLiteCpp.dir/src/Savepoint.cpp.i
.PHONY : src/Savepoint.cpp.i

src/Savepoint.s: src/Savepoint.cpp.s
.PHONY : src/Savepoint.s

# target to generate assembly for a file
src/Savepoint.cpp.s:
	cd /home/<USER>/OurChat/server/build && $(MAKE) $(MAKESILENT) -f thirdparty/SQLiteCpp/CMakeFiles/SQLiteCpp.dir/build.make thirdparty/SQLiteCpp/CMakeFiles/SQLiteCpp.dir/src/Savepoint.cpp.s
.PHONY : src/Savepoint.cpp.s

src/Statement.o: src/Statement.cpp.o
.PHONY : src/Statement.o

# target to build an object file
src/Statement.cpp.o:
	cd /home/<USER>/OurChat/server/build && $(MAKE) $(MAKESILENT) -f thirdparty/SQLiteCpp/CMakeFiles/SQLiteCpp.dir/build.make thirdparty/SQLiteCpp/CMakeFiles/SQLiteCpp.dir/src/Statement.cpp.o
.PHONY : src/Statement.cpp.o

src/Statement.i: src/Statement.cpp.i
.PHONY : src/Statement.i

# target to preprocess a source file
src/Statement.cpp.i:
	cd /home/<USER>/OurChat/server/build && $(MAKE) $(MAKESILENT) -f thirdparty/SQLiteCpp/CMakeFiles/SQLiteCpp.dir/build.make thirdparty/SQLiteCpp/CMakeFiles/SQLiteCpp.dir/src/Statement.cpp.i
.PHONY : src/Statement.cpp.i

src/Statement.s: src/Statement.cpp.s
.PHONY : src/Statement.s

# target to generate assembly for a file
src/Statement.cpp.s:
	cd /home/<USER>/OurChat/server/build && $(MAKE) $(MAKESILENT) -f thirdparty/SQLiteCpp/CMakeFiles/SQLiteCpp.dir/build.make thirdparty/SQLiteCpp/CMakeFiles/SQLiteCpp.dir/src/Statement.cpp.s
.PHONY : src/Statement.cpp.s

src/Transaction.o: src/Transaction.cpp.o
.PHONY : src/Transaction.o

# target to build an object file
src/Transaction.cpp.o:
	cd /home/<USER>/OurChat/server/build && $(MAKE) $(MAKESILENT) -f thirdparty/SQLiteCpp/CMakeFiles/SQLiteCpp.dir/build.make thirdparty/SQLiteCpp/CMakeFiles/SQLiteCpp.dir/src/Transaction.cpp.o
.PHONY : src/Transaction.cpp.o

src/Transaction.i: src/Transaction.cpp.i
.PHONY : src/Transaction.i

# target to preprocess a source file
src/Transaction.cpp.i:
	cd /home/<USER>/OurChat/server/build && $(MAKE) $(MAKESILENT) -f thirdparty/SQLiteCpp/CMakeFiles/SQLiteCpp.dir/build.make thirdparty/SQLiteCpp/CMakeFiles/SQLiteCpp.dir/src/Transaction.cpp.i
.PHONY : src/Transaction.cpp.i

src/Transaction.s: src/Transaction.cpp.s
.PHONY : src/Transaction.s

# target to generate assembly for a file
src/Transaction.cpp.s:
	cd /home/<USER>/OurChat/server/build && $(MAKE) $(MAKESILENT) -f thirdparty/SQLiteCpp/CMakeFiles/SQLiteCpp.dir/build.make thirdparty/SQLiteCpp/CMakeFiles/SQLiteCpp.dir/src/Transaction.cpp.s
.PHONY : src/Transaction.cpp.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... edit_cache"
	@echo "... install"
	@echo "... install/local"
	@echo "... install/strip"
	@echo "... list_install_components"
	@echo "... rebuild_cache"
	@echo "... SQLiteCpp_cpplint"
	@echo "... SQLiteCpp"
	@echo "... src/Backup.o"
	@echo "... src/Backup.i"
	@echo "... src/Backup.s"
	@echo "... src/Column.o"
	@echo "... src/Column.i"
	@echo "... src/Column.s"
	@echo "... src/Database.o"
	@echo "... src/Database.i"
	@echo "... src/Database.s"
	@echo "... src/Exception.o"
	@echo "... src/Exception.i"
	@echo "... src/Exception.s"
	@echo "... src/Savepoint.o"
	@echo "... src/Savepoint.i"
	@echo "... src/Savepoint.s"
	@echo "... src/Statement.o"
	@echo "... src/Statement.i"
	@echo "... src/Statement.s"
	@echo "... src/Transaction.o"
	@echo "... src/Transaction.i"
	@echo "... src/Transaction.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	cd /home/<USER>/OurChat/server/build && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

