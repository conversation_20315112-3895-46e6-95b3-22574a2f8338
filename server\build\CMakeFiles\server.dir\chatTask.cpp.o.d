CMakeFiles/server.dir/chatTask.cpp.o: \
 /home/<USER>/OurChat/server/chatTask.cpp /usr/include/stdc-predef.h \
 /home/<USER>/OurChat/server/chatTask.h /home/<USER>/OurChat/server/common.h \
 /home/<USER>/OurChat/server/DeThread.h /home/<USER>/OurChat/server/infos.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/iostream \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/x86_64-redhat-linux/bits/c++config.h \
 /usr/include/bits/wordsize.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/x86_64-redhat-linux/bits/os_defines.h \
 /usr/include/features.h /usr/include/sys/cdefs.h \
 /usr/include/gnu/stubs.h /usr/include/gnu/stubs-64.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/x86_64-redhat-linux/bits/cpu_defines.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/ostream \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/ios \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/iosfwd \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/bits/stringfwd.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/bits/memoryfwd.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/bits/postypes.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/cwchar /usr/include/wchar.h \
 /usr/include/stdio.h \
 /opt/rh/devtoolset-8/root/usr/lib/gcc/x86_64-redhat-linux/8/include/stdarg.h \
 /usr/include/bits/wchar.h \
 /opt/rh/devtoolset-8/root/usr/lib/gcc/x86_64-redhat-linux/8/include/stddef.h \
 /usr/include/xlocale.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/exception \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/bits/exception.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/bits/exception_ptr.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/bits/exception_defines.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/bits/cxxabi_init_exception.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/typeinfo \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/bits/hash_bytes.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/new \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/bits/nested_exception.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/bits/move.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/bits/concept_check.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/type_traits \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/bits/char_traits.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/bits/stl_algobase.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/bits/functexcept.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/bits/cpp_type_traits.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/ext/type_traits.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/ext/numeric_traits.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/bits/stl_pair.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/bits/stl_iterator_base_types.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/bits/stl_iterator_base_funcs.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/debug/assertions.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/bits/stl_iterator.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/bits/ptr_traits.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/debug/debug.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/bits/predefined_ops.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/cstdint \
 /opt/rh/devtoolset-8/root/usr/lib/gcc/x86_64-redhat-linux/8/include/stdint.h \
 /usr/include/stdint.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/bits/localefwd.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/x86_64-redhat-linux/bits/c++locale.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/clocale \
 /usr/include/locale.h /usr/include/bits/locale.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/cctype /usr/include/ctype.h \
 /usr/include/bits/types.h /usr/include/bits/typesizes.h \
 /usr/include/endian.h /usr/include/bits/endian.h \
 /usr/include/bits/byteswap.h /usr/include/bits/byteswap-16.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/bits/ios_base.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/ext/atomicity.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/x86_64-redhat-linux/bits/gthr.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/x86_64-redhat-linux/bits/gthr-default.h \
 /usr/include/pthread.h /usr/include/sched.h /usr/include/time.h \
 /usr/include/bits/sched.h /usr/include/bits/time.h \
 /usr/include/bits/timex.h /usr/include/bits/pthreadtypes.h \
 /usr/include/bits/setjmp.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/x86_64-redhat-linux/bits/atomic_word.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/bits/locale_classes.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/string \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/bits/allocator.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/x86_64-redhat-linux/bits/c++allocator.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/ext/new_allocator.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/bits/ostream_insert.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/bits/cxxabi_forced.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/bits/stl_function.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/backward/binders.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/bits/range_access.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/initializer_list \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/bits/basic_string.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/ext/alloc_traits.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/bits/alloc_traits.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/string_view \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/limits \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/bits/functional_hash.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/bits/string_view.tcc \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/ext/string_conversions.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/cstdlib \
 /usr/include/stdlib.h /usr/include/bits/waitflags.h \
 /usr/include/bits/waitstatus.h /usr/include/sys/types.h \
 /usr/include/sys/select.h /usr/include/bits/select.h \
 /usr/include/bits/sigset.h /usr/include/sys/sysmacros.h \
 /usr/include/alloca.h /usr/include/bits/stdlib-float.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/bits/std_abs.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/cstdio /usr/include/libio.h \
 /usr/include/_G_config.h /usr/include/bits/stdio_lim.h \
 /usr/include/bits/sys_errlist.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/cerrno /usr/include/errno.h \
 /usr/include/bits/errno.h /usr/include/linux/errno.h \
 /usr/include/asm/errno.h /usr/include/asm-generic/errno.h \
 /usr/include/asm-generic/errno-base.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/bits/basic_string.tcc \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/bits/locale_classes.tcc \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/system_error \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/x86_64-redhat-linux/bits/error_constants.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/stdexcept \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/streambuf \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/bits/streambuf.tcc \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/bits/basic_ios.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/bits/locale_facets.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/cwctype \
 /usr/include/wctype.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/x86_64-redhat-linux/bits/ctype_base.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/bits/streambuf_iterator.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/x86_64-redhat-linux/bits/ctype_inline.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/bits/locale_facets.tcc \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/bits/basic_ios.tcc \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/bits/ostream.tcc \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/istream \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/bits/istream.tcc \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/mutex \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/tuple \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/utility \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/bits/stl_relops.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/array \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/bits/uses_allocator.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/bits/invoke.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/chrono \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/ratio \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/ctime \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/bits/parse_numbers.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/bits/std_mutex.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/thread \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/memory \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/bits/stl_construct.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/bits/stl_uninitialized.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/bits/stl_tempbuf.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/bits/stl_raw_storage_iter.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/ext/concurrence.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/bits/unique_ptr.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/bits/shared_ptr.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/bits/shared_ptr_base.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/bits/allocated_ptr.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/bits/refwrap.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/ext/aligned_buffer.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/bits/shared_ptr_atomic.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/bits/atomic_base.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/bits/atomic_lockfree_defines.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/backward/auto_ptr.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/stdlib.h \
 /usr/include/sys/socket.h /usr/include/sys/uio.h /usr/include/bits/uio.h \
 /usr/include/bits/socket.h /usr/include/bits/socket_type.h \
 /usr/include/bits/sockaddr.h /usr/include/asm/socket.h \
 /usr/include/asm-generic/socket.h /usr/include/asm/sockios.h \
 /usr/include/asm-generic/sockios.h /usr/include/unistd.h \
 /usr/include/bits/posix_opt.h /usr/include/bits/environments.h \
 /usr/include/bits/confname.h /usr/include/getopt.h /usr/include/string.h \
 /usr/include/netinet/in.h /usr/include/bits/in.h /usr/include/netdb.h \
 /usr/include/rpc/netdb.h /usr/include/bits/siginfo.h \
 /usr/include/bits/netdb.h /usr/include/arpa/inet.h /usr/include/assert.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/map \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/bits/stl_tree.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/bits/node_handle.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/optional \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/bits/enable_special_members.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/bits/stl_map.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/bits/stl_multimap.h \
 /home/<USER>/OurChat/server/thirdparty/SQLiteCpp/include/SQLiteCpp/SQLiteCpp.h \
 /home/<USER>/OurChat/server/thirdparty/SQLiteCpp/include/SQLiteCpp/SQLiteCppExport.h \
 /home/<USER>/OurChat/server/thirdparty/SQLiteCpp/include/SQLiteCpp/Assertion.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/cassert \
 /home/<USER>/OurChat/server/thirdparty/SQLiteCpp/include/SQLiteCpp/Exception.h \
 /home/<USER>/OurChat/server/thirdparty/SQLiteCpp/include/SQLiteCpp/Database.h \
 /home/<USER>/OurChat/server/thirdparty/SQLiteCpp/include/SQLiteCpp/Column.h \
 /home/<USER>/OurChat/server/thirdparty/SQLiteCpp/include/SQLiteCpp/Statement.h \
 /home/<USER>/OurChat/server/thirdparty/SQLiteCpp/include/SQLiteCpp/Utils.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/filesystem \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/bits/fs_fwd.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/bits/fs_path.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/vector \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/bits/stl_vector.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/bits/stl_bvector.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/bits/vector.tcc \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/locale \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/bits/locale_facets_nonio.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/x86_64-redhat-linux/bits/time_members.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/x86_64-redhat-linux/bits/messages_members.h \
 /usr/include/libintl.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/bits/codecvt.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/bits/locale_facets_nonio.tcc \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/bits/locale_conv.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/codecvt \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/bits/quoted_string.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/sstream \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/bits/sstream.tcc \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/bits/fs_dir.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/bits/fs_ops.h \
 /home/<USER>/OurChat/server/thirdparty/SQLiteCpp/include/SQLiteCpp/Transaction.h \
 /home/<USER>/OurChat/server/thirdparty/json-develop/include/nlohmann/json.hpp \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/algorithm \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/bits/stl_algo.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/bits/algorithmfwd.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/bits/stl_heap.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/bits/uniform_int_dist.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/cstddef \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/functional \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/bits/std_function.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/unordered_map \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/bits/hashtable.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/bits/hashtable_policy.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/bits/unordered_map.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/iterator \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/bits/stream_iterator.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/numeric \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/bits/stl_numeric.h \
 /home/<USER>/OurChat/server/thirdparty/json-develop/include/nlohmann/adl_serializer.hpp \
 /home/<USER>/OurChat/server/thirdparty/json-develop/include/nlohmann/detail/abi_macros.hpp \
 /home/<USER>/OurChat/server/thirdparty/json-develop/include/nlohmann/detail/conversions/from_json.hpp \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/forward_list \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/bits/forward_list.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/bits/forward_list.tcc \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/valarray \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/cmath /usr/include/math.h \
 /usr/include/bits/huge_val.h /usr/include/bits/huge_valf.h \
 /usr/include/bits/huge_vall.h /usr/include/bits/inf.h \
 /usr/include/bits/nan.h /usr/include/bits/mathdef.h \
 /usr/include/bits/mathcalls.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/bits/specfun.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/tr1/gamma.tcc \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/tr1/special_function_util.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/tr1/bessel_function.tcc \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/tr1/special_function_util.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/tr1/beta_function.tcc \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/tr1/ell_integral.tcc \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/tr1/exp_integral.tcc \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/tr1/hypergeometric.tcc \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/tr1/legendre_function.tcc \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/tr1/modified_bessel_func.tcc \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/tr1/poly_hermite.tcc \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/tr1/poly_laguerre.tcc \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/tr1/riemann_zeta.tcc \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/bits/valarray_array.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/bits/valarray_array.tcc \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/bits/valarray_before.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/bits/slice_array.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/bits/valarray_after.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/bits/gslice.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/bits/gslice_array.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/bits/mask_array.h \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/bits/indirect_array.h \
 /home/<USER>/OurChat/server/thirdparty/json-develop/include/nlohmann/detail/exceptions.hpp \
 /home/<USER>/OurChat/server/thirdparty/json-develop/include/nlohmann/detail/value_t.hpp \
 /home/<USER>/OurChat/server/thirdparty/json-develop/include/nlohmann/detail/macro_scope.hpp \
 /home/<USER>/OurChat/server/thirdparty/json-develop/include/nlohmann/detail/meta/detected.hpp \
 /home/<USER>/OurChat/server/thirdparty/json-develop/include/nlohmann/detail/meta/void_t.hpp \
 /home/<USER>/OurChat/server/thirdparty/json-develop/include/nlohmann/thirdparty/hedley/hedley.hpp \
 /home/<USER>/OurChat/server/thirdparty/json-develop/include/nlohmann/detail/string_escape.hpp \
 /home/<USER>/OurChat/server/thirdparty/json-develop/include/nlohmann/detail/input/position_t.hpp \
 /home/<USER>/OurChat/server/thirdparty/json-develop/include/nlohmann/detail/meta/cpp_future.hpp \
 /home/<USER>/OurChat/server/thirdparty/json-develop/include/nlohmann/detail/meta/type_traits.hpp \
 /home/<USER>/OurChat/server/thirdparty/json-develop/include/nlohmann/detail/iterators/iterator_traits.hpp \
 /home/<USER>/OurChat/server/thirdparty/json-develop/include/nlohmann/detail/meta/call_std/begin.hpp \
 /home/<USER>/OurChat/server/thirdparty/json-develop/include/nlohmann/detail/meta/call_std/end.hpp \
 /home/<USER>/OurChat/server/thirdparty/json-develop/include/nlohmann/json_fwd.hpp \
 /home/<USER>/OurChat/server/thirdparty/json-develop/include/nlohmann/detail/string_concat.hpp \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/cstring \
 /home/<USER>/OurChat/server/thirdparty/json-develop/include/nlohmann/detail/meta/identity_tag.hpp \
 /home/<USER>/OurChat/server/thirdparty/json-develop/include/nlohmann/detail/meta/std_fs.hpp \
 /home/<USER>/OurChat/server/thirdparty/json-develop/include/nlohmann/detail/conversions/to_json.hpp \
 /home/<USER>/OurChat/server/thirdparty/json-develop/include/nlohmann/detail/iterators/iteration_proxy.hpp \
 /home/<USER>/OurChat/server/thirdparty/json-develop/include/nlohmann/byte_container_with_subtype.hpp \
 /home/<USER>/OurChat/server/thirdparty/json-develop/include/nlohmann/detail/hash.hpp \
 /home/<USER>/OurChat/server/thirdparty/json-develop/include/nlohmann/detail/input/binary_reader.hpp \
 /home/<USER>/OurChat/server/thirdparty/json-develop/include/nlohmann/detail/input/input_adapters.hpp \
 /home/<USER>/OurChat/server/thirdparty/json-develop/include/nlohmann/detail/input/json_sax.hpp \
 /home/<USER>/OurChat/server/thirdparty/json-develop/include/nlohmann/detail/input/lexer.hpp \
 /home/<USER>/OurChat/server/thirdparty/json-develop/include/nlohmann/detail/meta/is_sax.hpp \
 /home/<USER>/OurChat/server/thirdparty/json-develop/include/nlohmann/detail/input/parser.hpp \
 /home/<USER>/OurChat/server/thirdparty/json-develop/include/nlohmann/detail/iterators/internal_iterator.hpp \
 /home/<USER>/OurChat/server/thirdparty/json-develop/include/nlohmann/detail/iterators/primitive_iterator.hpp \
 /home/<USER>/OurChat/server/thirdparty/json-develop/include/nlohmann/detail/iterators/iter_impl.hpp \
 /home/<USER>/OurChat/server/thirdparty/json-develop/include/nlohmann/detail/iterators/json_reverse_iterator.hpp \
 /home/<USER>/OurChat/server/thirdparty/json-develop/include/nlohmann/detail/json_pointer.hpp \
 /home/<USER>/OurChat/server/thirdparty/json-develop/include/nlohmann/detail/json_ref.hpp \
 /home/<USER>/OurChat/server/thirdparty/json-develop/include/nlohmann/detail/output/binary_writer.hpp \
 /home/<USER>/OurChat/server/thirdparty/json-develop/include/nlohmann/detail/output/output_adapters.hpp \
 /home/<USER>/OurChat/server/thirdparty/json-develop/include/nlohmann/detail/output/serializer.hpp \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/iomanip \
 /home/<USER>/OurChat/server/thirdparty/json-develop/include/nlohmann/detail/conversions/to_chars.hpp \
 /home/<USER>/OurChat/server/thirdparty/json-develop/include/nlohmann/ordered_map.hpp \
 /opt/rh/devtoolset-8/root/usr/include/c++/8/any \
 /home/<USER>/OurChat/server/thirdparty/json-develop/include/nlohmann/detail/macro_unscope.hpp \
 /home/<USER>/OurChat/server/thirdparty/json-develop/include/nlohmann/thirdparty/hedley/hedley_undef.hpp \
 /home/<USER>/OurChat/server/session.h
