/****************************************************************************
** Meta object code from reading C++ file 'logging.h'
**
** Created by: The Qt Meta Object Compiler version 68 (Qt 6.5.3)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "../../../logging.h"
#include <QtGui/qtextcursor.h>
#include <QtNetwork/QSslError>
#include <QtCore/qmetatype.h>

#if __has_include(<QtCore/qtmochelpers.h>)
#include <QtCore/qtmochelpers.h>
#else
QT_BEGIN_MOC_NAMESPACE
#endif


#include <memory>

#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'logging.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 68
#error "This file was generated using the moc from 6.5.3. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

#ifndef Q_CONSTINIT
#define Q_CONSTINIT
#endif

QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
QT_WARNING_DISABLE_GCC("-Wuseless-cast")
namespace {

#ifdef QT_MOC_HAS_STRINGDATA
struct qt_meta_stringdata_CLASSLoggingENDCLASS_t {};
static constexpr auto qt_meta_stringdata_CLASSLoggingENDCLASS = QtMocHelpers::stringData(
    "Logging",
    "on_pushbtn_regist_clicked",
    "",
    "on_pushButton_login_clicked",
    "on_pushBtn_hide_clicked",
    "on_pushBtn_close_clicked",
    "on_pushBtn_close_2_clicked",
    "on_pushBtn_hide_2_clicked",
    "on_pushButton_regist_clicked",
    "on_pushButton_return_clicked",
    "on_pushButton_seePassword_clicked",
    "CmdHandler",
    "json",
    "msg"
);
#else  // !QT_MOC_HAS_STRING_DATA
struct qt_meta_stringdata_CLASSLoggingENDCLASS_t {
    uint offsetsAndSizes[28];
    char stringdata0[8];
    char stringdata1[26];
    char stringdata2[1];
    char stringdata3[28];
    char stringdata4[24];
    char stringdata5[25];
    char stringdata6[27];
    char stringdata7[26];
    char stringdata8[29];
    char stringdata9[29];
    char stringdata10[34];
    char stringdata11[11];
    char stringdata12[5];
    char stringdata13[4];
};
#define QT_MOC_LITERAL(ofs, len) \
    uint(sizeof(qt_meta_stringdata_CLASSLoggingENDCLASS_t::offsetsAndSizes) + ofs), len 
Q_CONSTINIT static const qt_meta_stringdata_CLASSLoggingENDCLASS_t qt_meta_stringdata_CLASSLoggingENDCLASS = {
    {
        QT_MOC_LITERAL(0, 7),  // "Logging"
        QT_MOC_LITERAL(8, 25),  // "on_pushbtn_regist_clicked"
        QT_MOC_LITERAL(34, 0),  // ""
        QT_MOC_LITERAL(35, 27),  // "on_pushButton_login_clicked"
        QT_MOC_LITERAL(63, 23),  // "on_pushBtn_hide_clicked"
        QT_MOC_LITERAL(87, 24),  // "on_pushBtn_close_clicked"
        QT_MOC_LITERAL(112, 26),  // "on_pushBtn_close_2_clicked"
        QT_MOC_LITERAL(139, 25),  // "on_pushBtn_hide_2_clicked"
        QT_MOC_LITERAL(165, 28),  // "on_pushButton_regist_clicked"
        QT_MOC_LITERAL(194, 28),  // "on_pushButton_return_clicked"
        QT_MOC_LITERAL(223, 33),  // "on_pushButton_seePassword_cli..."
        QT_MOC_LITERAL(257, 10),  // "CmdHandler"
        QT_MOC_LITERAL(268, 4),  // "json"
        QT_MOC_LITERAL(273, 3)   // "msg"
    },
    "Logging",
    "on_pushbtn_regist_clicked",
    "",
    "on_pushButton_login_clicked",
    "on_pushBtn_hide_clicked",
    "on_pushBtn_close_clicked",
    "on_pushBtn_close_2_clicked",
    "on_pushBtn_hide_2_clicked",
    "on_pushButton_regist_clicked",
    "on_pushButton_return_clicked",
    "on_pushButton_seePassword_clicked",
    "CmdHandler",
    "json",
    "msg"
};
#undef QT_MOC_LITERAL
#endif // !QT_MOC_HAS_STRING_DATA
} // unnamed namespace

Q_CONSTINIT static const uint qt_meta_data_CLASSLoggingENDCLASS[] = {

 // content:
      11,       // revision
       0,       // classname
       0,    0, // classinfo
      10,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       0,       // signalCount

 // slots: name, argc, parameters, tag, flags, initial metatype offsets
       1,    0,   74,    2, 0x08,    1 /* Private */,
       3,    0,   75,    2, 0x08,    2 /* Private */,
       4,    0,   76,    2, 0x08,    3 /* Private */,
       5,    0,   77,    2, 0x08,    4 /* Private */,
       6,    0,   78,    2, 0x08,    5 /* Private */,
       7,    0,   79,    2, 0x08,    6 /* Private */,
       8,    0,   80,    2, 0x08,    7 /* Private */,
       9,    0,   81,    2, 0x08,    8 /* Private */,
      10,    0,   82,    2, 0x08,    9 /* Private */,
      11,    1,   83,    2, 0x08,   10 /* Private */,

 // slots: parameters
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void, 0x80000000 | 12,   13,

       0        // eod
};

Q_CONSTINIT const QMetaObject Logging::staticMetaObject = { {
    QMetaObject::SuperData::link<QWidget::staticMetaObject>(),
    qt_meta_stringdata_CLASSLoggingENDCLASS.offsetsAndSizes,
    qt_meta_data_CLASSLoggingENDCLASS,
    qt_static_metacall,
    nullptr,
    qt_incomplete_metaTypeArray<qt_meta_stringdata_CLASSLoggingENDCLASS_t,
        // Q_OBJECT / Q_GADGET
        QtPrivate::TypeAndForceComplete<Logging, std::true_type>,
        // method 'on_pushbtn_regist_clicked'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'on_pushButton_login_clicked'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'on_pushBtn_hide_clicked'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'on_pushBtn_close_clicked'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'on_pushBtn_close_2_clicked'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'on_pushBtn_hide_2_clicked'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'on_pushButton_regist_clicked'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'on_pushButton_return_clicked'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'on_pushButton_seePassword_clicked'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'CmdHandler'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<json, std::false_type>
    >,
    nullptr
} };

void Logging::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<Logging *>(_o);
        (void)_t;
        switch (_id) {
        case 0: _t->on_pushbtn_regist_clicked(); break;
        case 1: _t->on_pushButton_login_clicked(); break;
        case 2: _t->on_pushBtn_hide_clicked(); break;
        case 3: _t->on_pushBtn_close_clicked(); break;
        case 4: _t->on_pushBtn_close_2_clicked(); break;
        case 5: _t->on_pushBtn_hide_2_clicked(); break;
        case 6: _t->on_pushButton_regist_clicked(); break;
        case 7: _t->on_pushButton_return_clicked(); break;
        case 8: _t->on_pushButton_seePassword_clicked(); break;
        case 9: _t->CmdHandler((*reinterpret_cast< std::add_pointer_t<json>>(_a[1]))); break;
        default: ;
        }
    }
}

const QMetaObject *Logging::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *Logging::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_CLASSLoggingENDCLASS.stringdata0))
        return static_cast<void*>(this);
    return QWidget::qt_metacast(_clname);
}

int Logging::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QWidget::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 10)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 10;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 10)
            *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType();
        _id -= 10;
    }
    return _id;
}
QT_WARNING_POP
