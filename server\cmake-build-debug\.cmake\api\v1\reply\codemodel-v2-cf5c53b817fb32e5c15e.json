{"configurations": [{"directories": [{"build": ".", "childIndexes": [1, 3], "hasInstallRule": true, "jsonFile": "directory-.-Debug-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.10"}, "projectIndex": 0, "source": ".", "targetIndexes": [2]}, {"build": "thirdparty/SQLiteCpp", "childIndexes": [2], "hasInstallRule": true, "jsonFile": "directory-thirdparty.SQLiteCpp-Debug-6d2886d86a235697c03e.json", "minimumCMakeVersion": {"string": "3.1"}, "parentIndex": 0, "projectIndex": 1, "source": "thirdparty/sQLitecpp", "targetIndexes": [0, 1]}, {"build": "thirdparty/SQLiteCpp/sqlite3", "hasInstallRule": true, "jsonFile": "directory-thirdparty.SQLiteCpp.sqlite3-Debug-a1837c480d95a6798718.json", "minimumCMakeVersion": {"string": "3.1"}, "parentIndex": 1, "projectIndex": 1, "source": "thirdparty/sQLitecpp/sqlite3", "targetIndexes": [3]}, {"build": "thirdparty/json-develop", "jsonFile": "directory-thirdparty.json-develop-Debug-eda85c858487e4cd1d56.json", "minimumCMakeVersion": {"string": "3.1"}, "parentIndex": 0, "projectIndex": 2, "source": "thirdparty/json-develop"}], "name": "Debug", "projects": [{"childIndexes": [1, 2], "directoryIndexes": [0], "name": "server", "targetIndexes": [2]}, {"directoryIndexes": [1, 2], "name": "SQLiteCpp", "parentIndex": 0, "targetIndexes": [0, 1, 3]}, {"directoryIndexes": [3], "name": "n<PERSON><PERSON>_json", "parentIndex": 0}], "targets": [{"directoryIndex": 1, "id": "SQLiteCpp::@7bfbb0359d215dd9211f", "jsonFile": "target-SQLiteCpp-Debug-95e9d4566189404ead41.json", "name": "SQLiteCpp", "projectIndex": 1}, {"directoryIndex": 1, "id": "SQLiteCpp_cpplint::@7bfbb0359d215dd9211f", "jsonFile": "target-SQLiteCpp_cpplint-Debug-c5830cd8fb9020ac28a5.json", "name": "SQLiteCpp_cpplint", "projectIndex": 1}, {"directoryIndex": 0, "id": "server::@6890427a1f51a3e7e1df", "jsonFile": "target-server-Debug-3f7f7dc52dd064a70a5a.json", "name": "server", "projectIndex": 0}, {"directoryIndex": 2, "id": "sqlite3::@741eb9df4aa093c9dadc", "jsonFile": "target-sqlite3-Debug-543d8651498a2b4c9daf.json", "name": "sqlite3", "projectIndex": 1}]}], "kind": "codemodel", "paths": {"build": "D:/Desktop/100/chat-forge-master/server/cmake-build-debug", "source": "D:/Desktop/100/chat-forge-master/server"}, "version": {"major": 2, "minor": 6}}