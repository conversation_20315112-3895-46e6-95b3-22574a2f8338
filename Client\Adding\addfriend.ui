<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>AddFriend</class>
 <widget class="QWidget" name="AddFriend">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>683</width>
    <height>480</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>查找</string>
  </property>
  <property name="windowIcon">
   <iconset resource="../src.qrc">
    <normaloff>:/pic/src/wechat2.png</normaloff>:/pic/src/wechat2.png</iconset>
  </property>
  <property name="layoutDirection">
   <enum>Qt::LeftToRight</enum>
  </property>
  <property name="styleSheet">
   <string notr="true">background-color: rgb(255, 255, 255);</string>
  </property>
  <widget class="QLineEdit" name="lineEdit">
   <property name="geometry">
    <rect>
     <x>160</x>
     <y>40</y>
     <width>250</width>
     <height>40</height>
    </rect>
   </property>
   <property name="styleSheet">
    <string notr="true">background-color: white;
    border: 1px solid gray;
    border-radius: 5px;
    padding: 2px 10px;
    font-size: 16px;
    color: black;</string>
   </property>
  </widget>
  <widget class="QPushButton" name="pushButton_search">
   <property name="geometry">
    <rect>
     <x>440</x>
     <y>40</y>
     <width>80</width>
     <height>40</height>
    </rect>
   </property>
   <property name="text">
    <string>查找</string>
   </property>
  </widget>
  <widget class="QPushButton" name="pushButton_add">
   <property name="geometry">
    <rect>
     <x>160</x>
     <y>410</y>
     <width>80</width>
     <height>40</height>
    </rect>
   </property>
   <property name="text">
    <string>添加</string>
   </property>
  </widget>
  <widget class="QPushButton" name="pushButton_cancel">
   <property name="geometry">
    <rect>
     <x>440</x>
     <y>410</y>
     <width>80</width>
     <height>40</height>
    </rect>
   </property>
   <property name="text">
    <string>取消</string>
   </property>
  </widget>
  <widget class="QTextEdit" name="textEdit">
   <property name="geometry">
    <rect>
     <x>160</x>
     <y>310</y>
     <width>361</width>
     <height>85</height>
    </rect>
   </property>
   <property name="accessibleDescription">
    <string>验证信息</string>
   </property>
   <property name="styleSheet">
    <string notr="true">background-color: white;
    border: 1px solid gray;
    border-radius: 5px;
    padding: 2px 10px;
    font-size: 16px;
    color: black;</string>
   </property>
  </widget>
  <widget class="QListWidget" name="listWidget">
   <property name="geometry">
    <rect>
     <x>160</x>
     <y>130</y>
     <width>361</width>
     <height>171</height>
    </rect>
   </property>
  </widget>
  <widget class="QRadioButton" name="radioButton_friend">
   <property name="geometry">
    <rect>
     <x>160</x>
     <y>100</y>
     <width>71</width>
     <height>21</height>
    </rect>
   </property>
   <property name="text">
    <string>好友</string>
   </property>
   <property name="checked">
    <bool>true</bool>
   </property>
  </widget>
  <widget class="QRadioButton" name="radioButton_group">
   <property name="geometry">
    <rect>
     <x>260</x>
     <y>100</y>
     <width>71</width>
     <height>21</height>
    </rect>
   </property>
   <property name="text">
    <string>群聊</string>
   </property>
  </widget>
 </widget>
 <resources>
  <include location="../src.qrc"/>
 </resources>
 <connections/>
</ui>
