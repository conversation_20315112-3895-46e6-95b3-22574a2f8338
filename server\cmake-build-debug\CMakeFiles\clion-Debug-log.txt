"D:\Program Files\JetBrains\CLion 2024.1.2\bin\cmake\win\x64\bin\cmake.exe" -DCMAKE_BUILD_TYPE=Debug "-DCMAKE_MAKE_PROGRAM=D:/Program Files/JetBrains/CLion 2024.1.2/bin/ninja/win/x64/ninja.exe" -G Ninja -S D:\Desktop\100\chat-forge-master\server -B D:\Desktop\100\chat-forge-master\server\cmake-build-debug
-- The C compiler identification is GNU 13.1.0
-- The CXX compiler identification is GNU 13.1.0
-- Detecting C compiler ABI info
-- Detecting C compiler ABI info - done
-- Check for working C compiler: D:/Program Files/JetBrains/CLion 2024.1.2/bin/mingw/bin/gcc.exe - skipped
-- Detecting C compile features
-- Detecting C compile features - done
-- Detecting CXX compiler ABI info
-- Detecting CXX compiler AB<PERSON> info - done
-- Check for working CXX compiler: D:/Program Files/JetBrains/CLion 2024.1.2/bin/mingw/bin/g++.exe - skipped
-- Detecting CXX compile features
-- Detecting CXX compile features - done
D:/Desktop/100/chat-forge-master/server
CMake Deprecation Warning at thirdparty/sQLitecpp/CMakeLists.txt:7 (cmake_minimum_required):
  Compatibility with CMake < 3.5 will be removed from a future version of
  CMake.

  Update the VERSION argument <min> value or use a ...<max> suffix to tell
  CMake that the project does not need compatibility with older versions.


-- Using c++ standard c++17
-- CMake version: 3.28.1
-- Project version: 3.2.1
-- CMAKE_CXX_COMPILER 'D:/Program Files/JetBrains/CLion 2024.1.2/bin/mingw/bin/g++.exe' 'GNU' '13.1.0'
-- CMAKE_CXX_FLAGS                ' -Wall -Wextra -Wpedantic -Wswitch-enum -Wshadow -Wno-long-long'
-- CMAKE_CXX_FLAGS_DEBUG          '-g'
-- Compile sqlite3 from source in subdirectory
CMake Warning (dev) at thirdparty/sQLitecpp/CMakeLists.txt:351 (find_package):
  Policy CMP0148 is not set: The FindPythonInterp and FindPythonLibs modules
  are removed.  Run "cmake --help-policy CMP0148" for policy details.  Use
  the cmake_policy command to set the policy and suppress this warning.

This warning is for project developers.  Use -Wno-dev to suppress it.

-- Found PythonInterp: E:/YingYong/py3.8/python.exe (found version "3.8.10") 
-- Could NOT find cppcheck
-- SQLITECPP_RUN_DOXYGEN OFF
-- SQLITECPP_BUILD_EXAMPLES OFF
-- SQLITECPP_BUILD_TESTS OFF
CMake Deprecation Warning at thirdparty/json-develop/CMakeLists.txt:1 (cmake_minimum_required):
  Compatibility with CMake < 3.5 will be removed from a future version of
  CMake.

  Update the VERSION argument <min> value or use a ...<max> suffix to tell
  CMake that the project does not need compatibility with older versions.


-- Using the multi-header code from D:/Desktop/100/chat-forge-master/server/thirdparty/json-develop/include/
-- Performing Test CMAKE_HAVE_LIBC_PTHREAD
-- Performing Test CMAKE_HAVE_LIBC_PTHREAD - Success
-- Found Threads: TRUE  
-- Configuring done (28.5s)
-- Generating done (0.0s)
-- Build files have been written to: D:/Desktop/100/chat-forge-master/server/cmake-build-debug
