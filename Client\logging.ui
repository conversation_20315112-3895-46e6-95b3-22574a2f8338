<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>Logging</class>
 <widget class="QWidget" name="Logging">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>564</width>
    <height>430</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <property name="windowIcon">
   <iconset resource="src.qrc">
    <normaloff>:/pic/src/wechat2.png</normaloff>:/pic/src/wechat2.png</iconset>
  </property>
  <widget class="QWidget" name="centerWidget" native="true">
   <property name="geometry">
    <rect>
     <x>0</x>
     <y>0</y>
     <width>561</width>
     <height>430</height>
    </rect>
   </property>
   <property name="minimumSize">
    <size>
     <width>430</width>
     <height>330</height>
    </size>
   </property>
   <property name="maximumSize">
    <size>
     <width>1000</width>
     <height>1000</height>
    </size>
   </property>
   <widget class="QStackedWidget" name="stackedWidget">
    <property name="geometry">
     <rect>
      <x>20</x>
      <y>10</y>
      <width>511</width>
      <height>401</height>
     </rect>
    </property>
    <property name="currentIndex">
     <number>0</number>
    </property>
    <widget class="QWidget" name="page">
     <widget class="QLabel" name="label">
      <property name="geometry">
       <rect>
        <x>54</x>
        <y>131</y>
        <width>61</width>
        <height>61</height>
       </rect>
      </property>
      <property name="font">
       <font>
        <pointsize>12</pointsize>
       </font>
      </property>
      <property name="layoutDirection">
       <enum>Qt::LeftToRight</enum>
      </property>
      <property name="text">
       <string>账号:</string>
      </property>
      <property name="alignment">
       <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
      </property>
     </widget>
     <widget class="QLabel" name="label_2">
      <property name="geometry">
       <rect>
        <x>54</x>
        <y>196</y>
        <width>61</width>
        <height>61</height>
       </rect>
      </property>
      <property name="font">
       <font>
        <pointsize>12</pointsize>
       </font>
      </property>
      <property name="text">
       <string>密码:</string>
      </property>
      <property name="alignment">
       <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
      </property>
     </widget>
     <widget class="QPushButton" name="pushBtn_close">
      <property name="geometry">
       <rect>
        <x>450</x>
        <y>0</y>
        <width>34</width>
        <height>40</height>
       </rect>
      </property>
      <property name="styleSheet">
       <string notr="true">QPushButton {
	border: none; /* no border for a flat push button */
	image: url(:/src/close.png); /* 最小化 */
	min-width: 34px;
	max-width: 34px;
	min-height: 40px;
	max-height: 40px;
}
QPushButton::hover {
	background-color: #f57575; 
}</string>
      </property>
      <property name="text">
       <string/>
      </property>
      <property name="icon">
       <iconset resource="src.qrc">
        <normaloff>:/pic/src/close.png</normaloff>:/pic/src/close.png</iconset>
      </property>
      <property name="iconSize">
       <size>
        <width>40</width>
        <height>40</height>
       </size>
      </property>
     </widget>
     <widget class="QLineEdit" name="lineEdit_account">
      <property name="geometry">
       <rect>
        <x>133</x>
        <y>135</y>
        <width>301</width>
        <height>51</height>
       </rect>
      </property>
      <property name="minimumSize">
       <size>
        <width>240</width>
        <height>22</height>
       </size>
      </property>
      <property name="maximumSize">
       <size>
        <width>2400</width>
        <height>2400</height>
       </size>
      </property>
      <property name="font">
       <font>
        <pointsize>12</pointsize>
       </font>
      </property>
      <property name="styleSheet">
       <string notr="true">QLineEdit{
	color: rgb(0, 0, 0);
	border-style: none;
	border-radius: 5px;	
	border-right:1px solid #3498db;
	border-top:1px solid #3498db;
	border-left:1px solid #3498db;
	border-bottom:1px solid #3498db;
	min-height: 20px;
}
QLineEdit:focus {
    border: 2px solid #3498db;
}</string>
      </property>
      <property name="text">
       <string>10001</string>
      </property>
      <property name="maxLength">
       <number>16</number>
      </property>
     </widget>

     <widget class="QLineEdit" name="lineEdit_password">
      <property name="geometry">
       <rect>
        <x>133</x>
        <y>202</y>
        <width>301</width>
        <height>51</height>
       </rect>
      </property>
      <property name="minimumSize">
       <size>
        <width>240</width>
        <height>22</height>
       </size>
      </property>
      <property name="maximumSize">
       <size>
        <width>2400</width>
        <height>3000</height>
       </size>
      </property>
      <property name="font">
       <font>
        <pointsize>12</pointsize>
       </font>
      </property>
      <property name="styleSheet">
       <string notr="true">QLineEdit{
	color: rgb(0, 0, 0);
	border-style: none;
	border-radius: 5px;	
	border-right:1px solid #3498db;
	border-top:1px solid #3498db;
	border-left:1px solid #3498db;
	border-bottom:1px solid #3498db;
	min-height: 20px;
}
QLineEdit:focus {
    border: 2px solid #3498db;
}</string>
      </property>
      <property name="text">
       <string>123</string>
      </property>
      <property name="maxLength">
       <number>32</number>
      </property>
      <property name="echoMode">
       <enum>QLineEdit::Password</enum>
      </property>
     </widget>
     <widget class="QPushButton" name="pushBtn_hide">
      <property name="geometry">
       <rect>
        <x>410</x>
        <y>0</y>
        <width>34</width>
        <height>40</height>
       </rect>
      </property>
      <property name="styleSheet">
       <string notr="true">QPushButton {
	border: none; /* no border for a flat push button */
	image: url(:/src/min.png); /* 最小化 */
	min-width: 34px;
	max-width: 34px;
	min-height: 40px;
	max-height: 40px;
}
QPushButton::hover {
	background-color: rgba(166,166,166, 50); 
}</string>
      </property>
      <property name="text">
       <string/>
      </property>
      <property name="icon">
       <iconset resource="src.qrc">
        <normaloff>:/pic/src/min.png</normaloff>:/pic/src/min.png</iconset>
      </property>
      <property name="iconSize">
       <size>
        <width>40</width>
        <height>40</height>
       </size>
      </property>
     </widget>
     <widget class="QLabel" name="label_4">
      <property name="geometry">
       <rect>
        <x>50</x>
        <y>10</y>
        <width>121</width>
        <height>41</height>
       </rect>
      </property>
      <property name="font">
       <font>
        <family>Consolas</family>
        <pointsize>15</pointsize>
       </font>
      </property>
      <property name="styleSheet">
       <string notr="true">color: rgb(166,166,166);</string>
      </property>
      <property name="text">
       <string>OurChat</string>
      </property>
     </widget>
     <widget class="QPushButton" name="pushButton_seePassword">
      <property name="geometry">
       <rect>
        <x>390</x>
        <y>210</y>
        <width>36</width>
        <height>36</height>
       </rect>
      </property>
      <property name="styleSheet">
       <string notr="true">border:none;</string>
      </property>
      <property name="text">
       <string/>
      </property>
      <property name="icon">
       <iconset resource="src.qrc">
        <normaloff>:/pic/src/eyes_open.png</normaloff>
        <normalon>:/pic/src/eyes_close.png</normalon>:/pic/src/eyes_open.png</iconset>
      </property>
      <property name="iconSize">
       <size>
        <width>36</width>
        <height>36</height>
       </size>
      </property>
      <property name="checkable">
       <bool>true</bool>
      </property>
     </widget>
     <widget class="QLabel" name="label_6">
      <property name="geometry">
       <rect>
        <x>0</x>
        <y>10</y>
        <width>40</width>
        <height>40</height>
       </rect>
      </property>
      <property name="text">
       <string/>
      </property>
      <property name="pixmap">
       <pixmap resource="src.qrc">:/pic/src/wechat2.png</pixmap>
      </property>
     </widget>
     <widget class="QPushButton" name="pushButton_login">
      <property name="geometry">
       <rect>
        <x>140</x>
        <y>330</y>
        <width>235</width>
        <height>51</height>
       </rect>
      </property>
      <property name="minimumSize">
       <size>
        <width>235</width>
        <height>35</height>
       </size>
      </property>
      <property name="styleSheet">
       <string notr="true">QPushButton{
	
	color: rgb(255, 255, 255);
	border-style: solid;
	border-radius: 5px;	
	background-color: rgb(7,193,96);
}

QPushButton:hover{
	background-color: rgb(56,205,127);
}

QPushButton:pressed {
	background-color: rgb(7,193,96);
}</string>
      </property>
      <property name="text">
       <string>登录</string>
      </property>
     </widget>
     <widget class="QPushButton" name="pushbtn_regist">
      <property name="geometry">
       <rect>
        <x>10</x>
        <y>360</y>
        <width>75</width>
        <height>23</height>
       </rect>
      </property>
      <property name="cursor">
       <cursorShape>ArrowCursor</cursorShape>
      </property>
      <property name="statusTip">
       <string>注册</string>
      </property>
      <property name="styleSheet">
       <string notr="true">QPushButton {
	color: rgb(166,166,166);
	border-style: none;
}
QPushButton::hover {
	color: rgb(67,129,202);
}</string>
      </property>
      <property name="text">
       <string>注册账号</string>
      </property>
     </widget>
    </widget>
    <widget class="QWidget" name="page_2">
     <widget class="QLabel" name="label_7">
      <property name="geometry">
       <rect>
        <x>83</x>
        <y>120</y>
        <width>81</width>
        <height>31</height>
       </rect>
      </property>
      <property name="font">
       <font>
        <pointsize>12</pointsize>
       </font>
      </property>
      <property name="text">
       <string>密码:</string>
      </property>
      <property name="alignment">
       <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
      </property>
     </widget>
     <widget class="QLineEdit" name="lineEdit_account_2">
      <property name="geometry">
       <rect>
        <x>200</x>
        <y>80</y>
        <width>240</width>
        <height>30</height>
       </rect>
      </property>
      <property name="minimumSize">
       <size>
        <width>240</width>
        <height>22</height>
       </size>
      </property>
      <property name="maximumSize">
       <size>
        <width>240</width>
        <height>30</height>
       </size>
      </property>
      <property name="styleSheet">
       <string notr="true">QLineEdit{
	color: rgb(0, 0, 0);
	border-style: none;
	border-radius: 5px;	
	border-right:1px solid #3498db;
	border-top:1px solid #3498db;
	border-left:1px solid #3498db;
	border-bottom:1px solid #3498db;
	min-height: 20px;
}
QLineEdit:focus {
    border: 2px solid #3498db;
}</string>
      </property>
      <property name="text">
       <string/>
      </property>
      <property name="maxLength">
       <number>16</number>
      </property>
      <property name="readOnly">
       <bool>true</bool>
      </property>
     </widget>
     <widget class="QPushButton" name="pushButton_generateAccount">
      <property name="geometry">
       <rect>
        <x>450</x>
        <y>80</y>
        <width>60</width>
        <height>30</height>
       </rect>
      </property>
      <property name="font">
       <font>
        <pointsize>9</pointsize>
       </font>
      </property>
      <property name="styleSheet">
       <string notr="true">QPushButton {
    background-color: #3498db;
    color: white;
    border: none;
    border-radius: 5px;
    padding: 5px;
}
QPushButton:hover {
    background-color: #2980b9;
}
QPushButton:pressed {
    background-color: #21618c;
}</string>
      </property>
      <property name="text">
       <string>生成</string>
      </property>
     </widget>
     <widget class="QLineEdit" name="lineEdit_password_2">
      <property name="geometry">
       <rect>
        <x>200</x>
        <y>120</y>
        <width>240</width>
        <height>30</height>
       </rect>
      </property>
      <property name="minimumSize">
       <size>
        <width>240</width>
        <height>22</height>
       </size>
      </property>
      <property name="maximumSize">
       <size>
        <width>240</width>
        <height>30</height>
       </size>
      </property>
      <property name="styleSheet">
       <string notr="true">QLineEdit{
	color: rgb(0, 0, 0);
	border-style: none;
	border-radius: 5px;	
	border-right:1px solid #3498db;
	border-top:1px solid #3498db;
	border-left:1px solid #3498db;
	border-bottom:1px solid #3498db;
	min-height: 20px;
}
QLineEdit:focus {
    border: 2px solid #3498db;
}</string>
      </property>
      <property name="text">
       <string/>
      </property>
      <property name="maxLength">
       <number>32</number>
      </property>
     </widget>
     <widget class="QLineEdit" name="lineEdit_password_3">
      <property name="geometry">
       <rect>
        <x>200</x>
        <y>160</y>
        <width>240</width>
        <height>30</height>
       </rect>
      </property>
      <property name="minimumSize">
       <size>
        <width>240</width>
        <height>22</height>
       </size>
      </property>
      <property name="maximumSize">
       <size>
        <width>240</width>
        <height>30</height>
       </size>
      </property>
      <property name="styleSheet">
       <string notr="true">QLineEdit{
	color: rgb(0, 0, 0);
	border-style: none;
	border-radius: 5px;	
	border-right:1px solid #3498db;
	border-top:1px solid #3498db;
	border-left:1px solid #3498db;
	border-bottom:1px solid #3498db;
	min-height: 20px;
}
QLineEdit:focus {
    border: 2px solid #3498db;
}</string>
      </property>
      <property name="text">
       <string/>
      </property>
      <property name="maxLength">
       <number>32</number>
      </property>
     </widget>
     <widget class="QLabel" name="label_8">
      <property name="geometry">
       <rect>
        <x>73</x>
        <y>160</y>
        <width>91</width>
        <height>31</height>
       </rect>
      </property>
      <property name="font">
       <font>
        <pointsize>12</pointsize>
       </font>
      </property>
      <property name="text">
       <string>确认密码:</string>
      </property>
      <property name="alignment">
       <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
      </property>
     </widget>
     <widget class="QLabel" name="label_9">
      <property name="geometry">
       <rect>
        <x>83</x>
        <y>80</y>
        <width>81</width>
        <height>31</height>
       </rect>
      </property>
      <property name="font">
       <font>
        <pointsize>12</pointsize>
       </font>
      </property>
      <property name="text">
       <string>账号:</string>
      </property>
      <property name="alignment">
       <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
      </property>
     </widget>
     <widget class="QLabel" name="label_10">
      <property name="geometry">
       <rect>
        <x>70</x>
        <y>200</y>
        <width>91</width>
        <height>31</height>
       </rect>
      </property>
      <property name="font">
       <font>
        <pointsize>12</pointsize>
       </font>
      </property>
      <property name="text">
       <string>昵称:</string>
      </property>
      <property name="alignment">
       <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
      </property>
     </widget>
     <widget class="QLineEdit" name="lineEdit_name">
      <property name="geometry">
       <rect>
        <x>200</x>
        <y>200</y>
        <width>240</width>
        <height>30</height>
       </rect>
      </property>
      <property name="minimumSize">
       <size>
        <width>240</width>
        <height>22</height>
       </size>
      </property>
      <property name="maximumSize">
       <size>
        <width>240</width>
        <height>30</height>
       </size>
      </property>
      <property name="styleSheet">
       <string notr="true">QLineEdit{
	color: rgb(0, 0, 0);
	border-style: none;
	border-radius: 5px;	
	border-right:1px solid #3498db;
	border-top:1px solid #3498db;
	border-left:1px solid #3498db;
	border-bottom:1px solid #3498db;
	min-height: 20px;
}
QLineEdit:focus {
    border: 2px solid #3498db;
}</string>
      </property>
      <property name="text">
       <string/>
      </property>
      <property name="maxLength">
       <number>32</number>
      </property>
     </widget>
     <widget class="QPushButton" name="pushButton_regist">
      <property name="geometry">
       <rect>
        <x>100</x>
        <y>290</y>
        <width>84</width>
        <height>24</height>
       </rect>
      </property>
      <property name="text">
       <string>注册</string>
      </property>
     </widget>
     <widget class="QPushButton" name="pushButton_return">
      <property name="geometry">
       <rect>
        <x>350</x>
        <y>290</y>
        <width>84</width>
        <height>24</height>
       </rect>
      </property>
      <property name="text">
       <string>返回</string>
      </property>
     </widget>
     <widget class="QLabel" name="label_5">
      <property name="geometry">
       <rect>
        <x>50</x>
        <y>10</y>
        <width>121</width>
        <height>41</height>
       </rect>
      </property>
      <property name="font">
       <font>
        <family>Consolas</family>
        <pointsize>15</pointsize>
       </font>
      </property>
      <property name="styleSheet">
       <string notr="true">color: rgb(166,166,166);</string>
      </property>
      <property name="text">
       <string>OurChat</string>
      </property>
     </widget>
     <widget class="QPushButton" name="pushBtn_close_2">
      <property name="geometry">
       <rect>
        <x>450</x>
        <y>0</y>
        <width>34</width>
        <height>40</height>
       </rect>
      </property>
      <property name="styleSheet">
       <string notr="true">QPushButton {
	border: none; /* no border for a flat push button */
	image: url(:/src/close.png); /* 最小化 */
	min-width: 34px;
	max-width: 34px;
	min-height: 40px;
	max-height: 40px;
}
QPushButton::hover {
	background-color: #f57575; 
}</string>
      </property>
      <property name="text">
       <string/>
      </property>
      <property name="icon">
       <iconset resource="src.qrc">
        <normaloff>:/pic/src/close.png</normaloff>:/pic/src/close.png</iconset>
      </property>
      <property name="iconSize">
       <size>
        <width>40</width>
        <height>40</height>
       </size>
      </property>
     </widget>
     <widget class="QPushButton" name="pushBtn_hide_2">
      <property name="geometry">
       <rect>
        <x>410</x>
        <y>0</y>
        <width>34</width>
        <height>40</height>
       </rect>
      </property>
      <property name="styleSheet">
       <string notr="true">QPushButton {
	border: none; /* no border for a flat push button */
	image: url(:/src/min.png); /* 最小化 */
	min-width: 34px;
	max-width: 34px;
	min-height: 40px;
	max-height: 40px;
}
QPushButton::hover {
	background-color: rgba(166,166,166, 50); 
}</string>
      </property>
      <property name="text">
       <string/>
      </property>
      <property name="icon">
       <iconset resource="src.qrc">
        <normaloff>:/pic/src/min.png</normaloff>:/pic/src/min.png</iconset>
      </property>
      <property name="iconSize">
       <size>
        <width>40</width>
        <height>40</height>
       </size>
      </property>
     </widget>
     <widget class="QLabel" name="label_11">
      <property name="geometry">
       <rect>
        <x>0</x>
        <y>10</y>
        <width>40</width>
        <height>40</height>
       </rect>
      </property>
      <property name="text">
       <string/>
      </property>
      <property name="pixmap">
       <pixmap resource="src.qrc">:/pic/src/wechat2.png</pixmap>
      </property>
     </widget>
    </widget>
   </widget>
  </widget>
 </widget>
 <resources>
  <include location="src.qrc"/>
 </resources>
 <connections/>
</ui>
