QMAKE_CXX.QT_COMPILER_STDCXX = 199711L
QMAKE_CXX.QMAKE_MSC_VER = 1944
QMAKE_CXX.QMAKE_MSC_FULL_VER = 194435207
QMAKE_CXX.COMPILER_MACROS = \
    QT_COMPILER_STDCXX \
    QMAKE_MSC_VER \
    QMAKE_MSC_FULL_VER
QMAKE_CXX.INCDIRS = \
    E:\\YingYong\\VisualStudio\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include \
    E:\\YingYong\\VisualStudio\\Community\\VC\\Tools\\MSVC\\14.44.35207\\ATLMFC\\include \
    E:\\YingYong\\VisualStudio\\Community\\VC\\Auxiliary\\VS\\include \
    "D:\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt" \
    "D:\\Windows Kits\\10\\\\include\\10.0.26100.0\\\\um" \
    "D:\\Windows Kits\\10\\\\include\\10.0.26100.0\\\\shared" \
    "D:\\Windows Kits\\10\\\\include\\10.0.26100.0\\\\winrt" \
    "D:\\Windows Kits\\10\\\\include\\10.0.26100.0\\\\cppwinrt" \
    "C:\\Program Files (x86)\\Windows Kits\\NETFXSDK\\4.8\\include\\um"
QMAKE_CXX.LIBDIRS = \
    E:\\YingYong\\VisualStudio\\Community\\VC\\Tools\\MSVC\\14.44.35207\\ATLMFC\\lib\\x64 \
    E:\\YingYong\\VisualStudio\\Community\\VC\\Tools\\MSVC\\14.44.35207\\lib\\x64 \
    "C:\\Program Files (x86)\\Windows Kits\\NETFXSDK\\4.8\\lib\\um\\x64" \
    "D:\\Windows Kits\\10\\lib\\10.0.26100.0\\ucrt\\x64" \
    "D:\\Windows Kits\\10\\\\lib\\10.0.26100.0\\\\um\\x64"
