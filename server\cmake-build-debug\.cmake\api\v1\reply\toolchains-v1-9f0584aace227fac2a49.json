{"kind": "toolchains", "toolchains": [{"compiler": {"id": "GNU", "implicit": {"includeDirectories": ["D:/Program Files/JetBrains/CLion 2024.1.2/bin/mingw/lib/gcc/x86_64-w64-mingw32/13.1.0/include", "D:/Program Files/JetBrains/CLion 2024.1.2/bin/mingw/include", "D:/Program Files/JetBrains/CLion 2024.1.2/bin/mingw/lib/gcc/x86_64-w64-mingw32/13.1.0/include-fixed", "D:/Program Files/JetBrains/CLion 2024.1.2/bin/mingw/x86_64-w64-mingw32/include"], "linkDirectories": [], "linkFrameworkDirectories": [], "linkLibraries": []}, "path": "D:/Program Files/JetBrains/CLion 2024.1.2/bin/mingw/bin/gcc.exe", "version": "13.1.0"}, "language": "C", "sourceFileExtensions": ["c", "m"]}, {"compiler": {"id": "GNU", "implicit": {"includeDirectories": ["D:/Program Files/JetBrains/CLion 2024.1.2/bin/mingw/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++", "D:/Program Files/JetBrains/CLion 2024.1.2/bin/mingw/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/x86_64-w64-mingw32", "D:/Program Files/JetBrains/CLion 2024.1.2/bin/mingw/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/backward", "D:/Program Files/JetBrains/CLion 2024.1.2/bin/mingw/lib/gcc/x86_64-w64-mingw32/13.1.0/include", "D:/Program Files/JetBrains/CLion 2024.1.2/bin/mingw/include", "D:/Program Files/JetBrains/CLion 2024.1.2/bin/mingw/lib/gcc/x86_64-w64-mingw32/13.1.0/include-fixed", "D:/Program Files/JetBrains/CLion 2024.1.2/bin/mingw/x86_64-w64-mingw32/include"], "linkDirectories": [], "linkFrameworkDirectories": [], "linkLibraries": []}, "path": "D:/Program Files/JetBrains/CLion 2024.1.2/bin/mingw/bin/g++.exe", "version": "13.1.0"}, "language": "CXX", "sourceFileExtensions": ["C", "M", "c++", "cc", "cpp", "cxx", "mm", "mpp", "CPP", "ixx", "cppm", "ccm", "cxxm", "c++m"]}, {"compiler": {"implicit": {}, "path": "D:/Program Files/JetBrains/CLion 2024.1.2/bin/mingw/bin/windres.exe"}, "language": "RC", "sourceFileExtensions": ["rc", "RC"]}], "version": {"major": 1, "minor": 0}}